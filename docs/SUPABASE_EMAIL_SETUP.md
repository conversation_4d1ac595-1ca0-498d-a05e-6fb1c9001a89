# Supabase Email Verification Setup

## 配置自定义邮件验证链接

为了让邮件中的验证链接指向我们自己的域名而不是 Supabase 的域名，需要进行以下配置：

### 1. Supabase 项目设置

1. 登录 Supabase Dashboard
2. 进入你的项目
3. 导航到 `Authentication` > `Settings`
4. 在 `Site URL` 中设置你的网站域名：
   ```
   https://your-domain.com
   ```

5. 在 `Redirect URLs` 中添加以下URL：
   ```
   https://your-domain.com/auth/verify
   https://your-domain.com/en/complete-registration
   https://your-domain.com/zh/complete-registration
   https://your-domain.com/ja/complete-registration
   ```

### 2. 邮件模板配置

1. 在 Supabase Dashboard 中，导航到 `Authentication` > `Email Templates`
2. 选择 `Magic Link` 模板
3. 自定义邮件内容，确保链接指向正确的验证页面

默认的邮件模板会包含类似这样的链接：
```
{{ .SiteURL }}/auth/verify?token_hash={{ .TokenHash }}&type=email
```

### 3. 环境变量配置

确保在 `.env.local` 文件中设置了正确的站点URL：

```env
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

对于本地开发，可以使用：
```env
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 4. 验证流程说明

新的验证流程如下：

1. **用户注册**：用户在 `/register` 页面输入邮箱
2. **发送邮件**：系统调用 `/api/auth/send-verification` 发送验证邮件
3. **邮件内容**：邮件包含指向 `https://your-domain.com/auth/verify?token_hash=...&type=email&email=...` 的链接
4. **点击验证**：用户点击邮件中的链接
5. **验证处理**：`/auth/verify` 页面处理验证逻辑
6. **完成注册**：验证成功后重定向到 `/complete-registration` 页面

### 5. 本地开发测试

对于本地开发，你可以：

1. 使用 ngrok 或类似工具创建公网隧道：
   ```bash
   ngrok http 3000
   ```

2. 将生成的 ngrok URL 设置为 Supabase 的 Site URL

3. 或者直接在 Supabase 中添加 `http://localhost:3000` 作为允许的重定向URL

### 6. 邮件模板示例

你可以自定义邮件模板，例如：

```html
<h2>Welcome to Oisii!</h2>
<p>Thank you for registering with Oisii. Please click the link below to verify your email address and complete your registration:</p>
<p><a href="{{ .SiteURL }}/auth/verify?token_hash={{ .TokenHash }}&type=email&email={{ .Email }}">Verify Email Address</a></p>
<p>If you didn't create an account with us, please ignore this email.</p>
<p>Best regards,<br>The Oisii Team</p>
```

### 7. 故障排除

**问题：邮件中的链接仍然指向 supabase.co**
- 检查 Supabase 项目的 Site URL 设置
- 确保 NEXT_PUBLIC_SITE_URL 环境变量正确设置
- 重新发送测试邮件

**问题：验证页面显示错误**
- 检查 URL 参数是否正确传递
- 查看浏览器控制台的错误信息
- 确认 Supabase 客户端配置正确

**问题：验证成功但没有重定向**
- 检查 `/auth/verify` 页面的重定向逻辑
- 确认目标页面路径正确
- 查看网络请求是否成功

### 8. 安全注意事项

1. 确保所有重定向URL都在 Supabase 的允许列表中
2. 验证 token 的有效性和过期时间
3. 在生产环境中使用 HTTPS
4. 定期检查和更新邮件模板

这样配置后，用户收到的验证邮件将包含指向你自己域名的链接，提供更好的品牌体验和用户信任度。
