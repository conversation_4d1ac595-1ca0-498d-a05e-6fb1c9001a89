# Security Architecture - Registration Flow

## 架构原则

### 🔒 前端安全原则
- **零直接访问**：前端永不直接访问 Supabase
- **API 代理**：所有数据库操作通过 API 路由
- **密钥保护**：敏感密钥仅在服务器端使用

## 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Routes    │    │   Supabase      │
│   (Browser)     │    │   (Server)      │    │   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. POST /api/auth/    │                       │
         │    send-verification  │                       │
         ├──────────────────────►│                       │
         │                       │ 2. supabase.auth     │
         │                       │    .signInWithOtp()  │
         │                       ├──────────────────────►│
         │                       │                       │
         │                       │ 3. Send Email         │
         │                       │◄──────────────────────┤
         │ 4. Success Response   │                       │
         │◄──────────────────────┤                       │
         │                       │                       │
         │ 5. User clicks email  │                       │
         │    link → /auth/verify│                       │
         │                       │                       │
         │ 6. POST /api/auth/    │                       │
         │    verify             │                       │
         ├──────────────────────►│                       │
         │                       │ 7. supabase.auth     │
         │                       │    .verifyOtp()       │
         │                       ├──────────────────────►│
         │                       │                       │
         │                       │ 8. Verification       │
         │                       │    Result             │
         │                       │◄──────────────────────┤
         │ 9. Success Response   │                       │
         │◄──────────────────────┤                       │
         │                       │                       │
         │ 10. POST /api/auth/   │                       │
         │     complete-         │                       │
         │     registration      │                       │
         ├──────────────────────►│                       │
         │                       │ 11. Create Restaurant,│
         │                       │     User, Services    │
         │                       ├──────────────────────►│
         │                       │                       │
         │                       │ 12. Database Updates │
         │                       │◄──────────────────────┤
         │ 13. Success Response  │                       │
         │◄──────────────────────┤                       │
```

## 安全层级

### 第一层：前端安全
```typescript
// ❌ 错误做法 - 前端直接访问
const supabase = createClient(url, key);
await supabase.auth.verifyOtp(token);

// ✅ 正确做法 - 通过 API
const response = await fetch('/api/auth/verify', {
  method: 'POST',
  body: JSON.stringify({ token_hash, type })
});
```

### 第二层：API 路由安全
```typescript
// API 路由中的安全实践
export async function POST(request: NextRequest) {
  try {
    // 1. 输入验证
    const { token_hash, type } = await request.json();
    if (!token_hash || !type) {
      return NextResponse.json({ error: 'Invalid input' }, { status: 400 });
    }

    // 2. 服务器端 Supabase 客户端
    const supabase = await createClient(); // 使用服务器密钥

    // 3. 安全操作
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash,
      type: type as any,
    });

    // 4. 错误处理
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // 5. 返回最小必要信息
    return NextResponse.json({
      success: true,
      email: data.user?.email,
    });
  } catch (error) {
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
}
```

### 第三层：数据库安全
- **RLS (Row Level Security)**：确保用户只能访问自己的数据
- **角色权限**：基于角色的访问控制
- **服务密钥**：仅在服务器端使用

## 密钥管理

### 环境变量分离
```env
# 公开密钥 - 可以在前端使用
NEXT_PUBLIC_SUPABASE_URL=https://project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...

# 私有密钥 - 仅服务器端使用
SUPABASE_SERVICE_KEY=eyJ...  # 🔒 绝不暴露给前端
```

### 密钥使用规则
1. **ANON_KEY**：前端可见，权限受限
2. **SERVICE_KEY**：服务器专用，完全权限
3. **分离原则**：前端永不接触 SERVICE_KEY

## 验证流程安全

### 1. 邮件发送安全
```typescript
// /api/auth/send-verification
- 输入验证：邮箱格式检查
- 频率限制：防止邮件轰炸
- 语言参数：安全传递用户偏好
```

### 2. 邮件验证安全
```typescript
// /api/auth/verify
- Token 验证：服务器端处理
- 时效检查：自动过期处理
- 错误处理：不泄露敏感信息
```

### 3. 注册完成安全
```typescript
// /api/auth/complete-registration
- 事务处理：确保数据一致性
- 回滚机制：失败时清理数据
- 权限分配：自动角色分配
```

## 攻击防护

### 1. CSRF 防护
- 使用 Next.js 内置 CSRF 保护
- API 路由自动验证来源

### 2. 注入攻击防护
- 参数化查询
- 输入验证和清理
- Supabase 内置 SQL 注入防护

### 3. 暴力攻击防护
- 邮件发送频率限制
- 验证尝试次数限制
- IP 级别的访问控制

### 4. 信息泄露防护
- 最小化错误信息
- 不暴露内部系统细节
- 日志中不记录敏感信息

## 监控和审计

### 1. 安全日志
```typescript
// 记录关键安全事件
console.log('Email verification attempt:', {
  email: email.replace(/(.{2}).*(@.*)/, '$1***$2'), // 脱敏
  timestamp: new Date().toISOString(),
  ip: request.ip,
  userAgent: request.headers.get('user-agent')
});
```

### 2. 异常监控
- 失败的验证尝试
- 异常的注册模式
- API 错误率监控

### 3. 性能监控
- API 响应时间
- 数据库查询性能
- 邮件发送成功率

## 最佳实践总结

### ✅ 应该做的
1. 所有 Supabase 操作通过 API 路由
2. 使用服务器端 Supabase 客户端
3. 实施输入验证和错误处理
4. 记录安全相关事件
5. 定期审查和更新安全措施

### ❌ 不应该做的
1. 前端直接访问 Supabase
2. 在前端暴露 SERVICE_KEY
3. 忽略输入验证
4. 返回详细的错误信息给前端
5. 在日志中记录敏感信息

这种架构确保了系统的安全性，同时保持了良好的用户体验和开发效率。
