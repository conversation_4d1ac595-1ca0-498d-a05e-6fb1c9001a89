# 权限系统总结

## 角色权限矩阵

| 角色 | 管理员页面 | 厨房页面 | 数据访问范围 | 说明 |
|------|------------|----------|--------------|------|
| `super_admin` | ✅ | ✅ | 所有餐厅 | 超级管理员，可访问所有功能和数据 |
| `admin` | ✅ | ✅ | 所有餐厅 | 管理员，可访问所有功能和数据 |
| `restaurant_owner` | ❌ | ✅ | 自己的餐厅 | 餐厅老板，与kitchen角色权限相同 |
| `kitchen` | ❌ | ✅ | 自己的餐厅 | 厨房工作人员，只能访问厨房功能 |

## 权限检查实现

### 1. 统一权限系统 (`src/lib/auth/permissions.ts`)

```typescript
// 厨房访问权限检查
export function hasKitchenAccess(permissions: UserPermissions): boolean {
  return permissions.isAdmin || permissions.isKitchen || permissions.isRestaurantOwner;
}

// 管理员权限检查
export function hasAdminAccess(permissions: UserPermissions): boolean {
  return permissions.isAdmin;
}

// 餐厅数据访问权限
export function canAccessRestaurant(permissions: UserPermissions, targetRestaurantId: string): boolean {
  // 管理员可以访问所有餐厅
  if (permissions.isAdmin) {
    return true;
  }
  
  // 其他用户只能访问自己的餐厅
  return permissions.restaurantId === targetRestaurantId;
}
```

### 2. 中间件权限检查 (`src/middleware.ts`)

```typescript
// 管理员页面权限
if (isAdminRoute && roleName !== 'super_admin' && roleName !== 'admin') {
  // 重定向到厨房页面或首页
}

// 厨房页面权限
if (isKitchenRoute && roleName !== 'super_admin' && roleName !== 'admin' && 
    roleName !== 'kitchen' && roleName !== 'restaurant_owner') {
  // 重定向到首页
}
```

### 3. API权限检查

#### 已更新使用统一权限系统的API：
- ✅ `src/app/api/kitchen/orders/route.ts`
- ✅ `src/app/api/kitchen/dishes/route.ts`
- ✅ `src/app/api/kitchen/stats/route.ts`

#### 待更新的API（仍使用旧权限检查）：
- 🔄 `src/app/api/kitchen/dishes/[id]/route.ts`
- 🔄 `src/app/api/kitchen/order-items/update-status/route.ts`
- 🔄 `src/app/api/socket/route.ts`
- 🔄 `src/app/api/kitchen/manual-order/route.ts`
- 🔄 `src/app/api/kitchen/categories/[id]/route.ts`
- 🔄 `src/app/api/kitchen/categories/route.ts`
- 🔄 `src/app/api/kitchen/orders/update-status/route.ts`
- 🔄 `src/app/api/upload/presigned-url/route.ts`
- 🔄 `src/app/api/kitchen/dishes/reorder/route.ts`
- 🔄 `src/app/api/kitchen/orders/history/route.ts`
- 🔄 `src/app/api/kitchen/order-items/update-quantity/route.ts`
- 🔄 `src/app/api/kitchen/categories/reorder/route.ts`
- 🔄 `src/app/api/tables/route.ts`

## 关键修复

### ✅ 已修复的问题

1. **权限系统统一**：
   - `restaurant_owner` 现在与 `kitchen` 有相同的权限
   - 使用 `hasKitchenAccess()` 函数统一检查厨房权限

2. **数据隔离**：
   - 非管理员用户只能访问自己餐厅的数据
   - 使用服务器端验证的用户餐厅ID，而不是前端参数

3. **登录状态清理**：
   - 登录时清理之前用户的localStorage数据
   - 防止用户看到其他餐厅的数据

### 🔧 权限检查逻辑

```typescript
// 新的统一权限检查模式
export async function requireKitchenAccess(request: NextRequest): Promise<UserPermissions | NextResponse> {
  const permissions = await validateUserPermissions(request);
  
  if (!permissions) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // 检查厨房访问权限（包括 restaurant_owner）
  if (!hasKitchenAccess(permissions)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  return permissions;
}
```

## 安全特性

1. **多租户数据隔离**：每个餐厅只能访问自己的数据
2. **角色基础访问控制**：基于用户角色限制功能访问
3. **服务器端验证**：所有权限检查在服务器端进行
4. **会话管理**：登录时清理之前的用户状态

## 测试要点

1. **restaurant_owner 用户应该能够**：
   - 访问厨房页面 (`/kitchen`)
   - 查看自己餐厅的订单
   - 管理自己餐厅的菜品和分类
   - 查看自己餐厅的统计数据

2. **restaurant_owner 用户不应该能够**：
   - 访问管理员页面 (`/admin`)
   - 查看其他餐厅的数据
   - 执行系统级管理操作

3. **数据隔离测试**：
   - 切换用户后应该看到不同的餐厅数据
   - 无法通过修改前端参数访问其他餐厅数据

## 下一步

继续更新剩余的API文件，使用统一的权限系统，确保所有厨房相关功能都正确支持 `restaurant_owner` 角色。
