# Registration Flow Documentation

## Overview

The Oisii platform uses a secure email verification-based registration flow powered by Supabase Auth. This ensures that only verified email addresses can create restaurant accounts.

## Registration Process

### Step 1: Email Verification Request
1. User visits `/register` page
2. User enters their email address
3. System sends verification email via Supabase Auth
4. User receives email with verification link

### Step 2: Email Verification
1. User clicks verification link in email (points to your domain, not Supabase)
2. <PERSON> redirects to `/auth/verify` page on your website
3. `/auth/verify` page processes the verification with Supabase
4. After successful verification, user is redirected to `/complete-registration` page

### Step 3: Complete Registration
1. User enters restaurant name and password
2. System creates:
   - Restaurant record
   - User account with restaurant owner role
   - Service subscription (Basic plan)
   - Free trial or permanent free access (first 10 restaurants)

## API Endpoints

### POST /api/auth/send-verification
Sends email verification link to the provided email address.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "language": "en"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification email sent successfully"
}
```

### POST /api/auth/complete-registration
Completes the registration process after email verification.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "restaurantName": "My Restaurant",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration completed successfully",
  "restaurant": {
    "id": "uuid",
    "name": "My Restaurant"
  },
  "service": {
    "plan": "basic",
    "isTrial": true,
    "endDate": "2024-11-01T00:00:00.000Z",
    "isFreeEligible": true
  }
}
```

## Service Plans

### Free Tier (First 10 Restaurants)
- **Duration**: Permanent
- **Features**: Full Basic plan features
- **Cost**: Free forever

### Trial Tier (All Other Restaurants)
- **Duration**: 3 months
- **Features**: Full Basic plan features
- **Cost**: Free for trial period

### Basic Plan Features
- QR code ordering system
- Kitchen management interface
- Multi-language support (Chinese, Japanese, English, Korean)
- Real-time order updates
- Basic analytics

## Database Schema

### Tables Created During Registration

1. **restaurants**
   - `id`: UUID (Primary Key)
   - `name`: Restaurant name
   - `is_active`: Boolean (default: true)
   - `created_at`: Timestamp

2. **restaurant_services**
   - `id`: UUID (Primary Key)
   - `restaurant_id`: Foreign key to restaurants
   - `service_plan_id`: Foreign key to service_plans
   - `start_date`: Service start date
   - `end_date`: Service end date
   - `is_trial`: Boolean indicating if this is a trial

3. **users**
   - `id`: UUID (Primary Key)
   - `auth_id`: Supabase Auth user ID
   - `email`: User email
   - `name`: User display name
   - `role_id`: Foreign key to roles (restaurant_owner)
   - `restaurant_id`: Foreign key to restaurants

## Environment Variables

Required environment variables for the registration flow:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## Security Features

1. **Email Verification**: Ensures only valid email addresses can register
2. **Role-Based Access**: Users are assigned restaurant_owner role with appropriate permissions
3. **Service Limits**: Automatic service expiration for trial users
4. **Data Isolation**: Restaurant owners can only access their own data

## Error Handling

The registration flow includes comprehensive error handling:

- Email validation
- Duplicate email prevention
- Service plan validation
- Database transaction rollback on failures
- User-friendly error messages in multiple languages

## Testing

To test the registration flow:

1. Set up Supabase project with required tables
2. Configure email templates in Supabase Auth
3. Set environment variables
4. Test email delivery
5. Verify complete registration flow

## Troubleshooting

Common issues and solutions:

1. **Email not received**: Check Supabase email configuration
2. **Registration fails**: Check database permissions and service keys
3. **Redirect issues**: Verify NEXT_PUBLIC_SITE_URL configuration
