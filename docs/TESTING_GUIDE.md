# Registration Flow Testing Guide

## 测试前准备

### 1. 环境配置

确保以下环境变量已正确设置：

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
NEXT_PUBLIC_SITE_URL=http://localhost:3000  # 本地开发
```

### 2. Supabase 配置

1. **Site URL 设置**：
   - 在 Supabase Dashboard > Authentication > Settings
   - 设置 Site URL 为 `http://localhost:3000`

2. **Redirect URLs 设置**：
   添加以下重定向URL：
   ```
   http://localhost:3000/auth/verify
   http://localhost:3000/en/complete-registration
   http://localhost:3000/zh/complete-registration
   http://localhost:3000/ja/complete-registration
   ```

3. **邮件模板**：
   - 确保邮件模板中的链接指向正确的验证页面
   - 默认模板应该包含：`{{ .SiteURL }}/auth/verify?token_hash={{ .TokenHash }}&type=email`

## 测试步骤

### 第一步：注册页面测试

1. 访问 `http://localhost:3000/en/register`
2. 输入有效的邮箱地址
3. 点击"Send Verification Email"
4. 验证：
   - [ ] 页面显示"验证邮件已发送"消息
   - [ ] 没有错误信息
   - [ ] 可以点击"重新发送邮件"

### 第二步：邮件验证测试

1. 检查邮箱收到验证邮件
2. 验证邮件内容：
   - [ ] 邮件来自 Supabase
   - [ ] 包含验证链接
   - [ ] 链接指向 `http://localhost:3000/auth/verify?...`
   - [ ] 链接包含正确的参数（token_hash, type, email, lang）

3. 点击邮件中的验证链接
4. 验证：
   - [ ] 重定向到验证页面
   - [ ] 显示"正在验证邮箱"加载状态
   - [ ] 验证成功后显示成功消息
   - [ ] 2秒后自动重定向到完成注册页面

### 第三步：完成注册测试

1. 在完成注册页面验证：
   - [ ] 页面显示"邮箱已验证：<EMAIL>"
   - [ ] 有餐厅名称输入框
   - [ ] 有密码输入框
   - [ ] 有确认密码输入框

2. 填写注册信息：
   - 输入餐厅名称
   - 输入密码
   - 确认密码

3. 点击"完成注册"
4. 验证：
   - [ ] 注册成功消息
   - [ ] 3秒后重定向到登录页面

### 第四步：数据库验证

在 Supabase Dashboard 中检查：

1. **restaurants 表**：
   - [ ] 新增了餐厅记录
   - [ ] `name` 字段正确
   - [ ] `is_active` 为 true

2. **users 表**：
   - [ ] 新增了用户记录
   - [ ] `email` 字段正确
   - [ ] `role_id` 指向 restaurant_owner 角色
   - [ ] `restaurant_id` 指向正确的餐厅

3. **restaurant_services 表**：
   - [ ] 新增了服务记录
   - [ ] `service_plan_id` 指向 basic 计划
   - [ ] `is_trial` 为 true
   - [ ] `end_date` 正确设置（前10家永久，其他3个月）

4. **Auth 表**：
   - [ ] 在 Authentication > Users 中看到新用户
   - [ ] 邮箱已验证

## 错误场景测试

### 1. 无效邮箱测试
- 输入无效邮箱格式
- 验证显示适当错误消息

### 2. 重复邮箱测试
- 使用已注册的邮箱
- 验证系统处理重复注册

### 3. 过期链接测试
- 使用过期的验证链接
- 验证显示适当错误消息

### 4. 密码不匹配测试
- 在完成注册页面输入不匹配的密码
- 验证显示错误消息

## 多语言测试

### 测试不同语言路径：

1. **英文**：`/en/register`
2. **中文**：`/zh/register`
3. **日文**：`/ja/register`

验证：
- [ ] 界面文本正确翻译
- [ ] 验证邮件包含正确的语言参数
- [ ] 验证成功后重定向到对应语言的完成注册页面

## 性能测试

### 1. 并发注册测试
- 同时进行多个注册流程
- 验证系统稳定性

### 2. 前10家免费测试
- 注册10家餐厅
- 验证前10家获得永久免费服务
- 验证第11家获得3个月试用

## 故障排除

### 常见问题：

1. **邮件未收到**：
   - 检查垃圾邮件文件夹
   - 验证 Supabase 邮件配置
   - 检查 Site URL 设置

2. **验证链接无效**：
   - 检查 Redirect URLs 配置
   - 验证环境变量设置
   - 查看浏览器控制台错误

3. **注册失败**：
   - 检查数据库权限
   - 验证 Service Key 配置
   - 查看服务器日志

4. **重定向问题**：
   - 检查路由配置
   - 验证中间件设置
   - 确认页面路径正确

## 自动化测试

可以使用以下工具进行自动化测试：

1. **Playwright** - 端到端测试
2. **Jest** - 单元测试
3. **Cypress** - 集成测试

示例测试脚本：

```javascript
// 注册流程端到端测试
test('complete registration flow', async ({ page }) => {
  // 访问注册页面
  await page.goto('/en/register');
  
  // 输入邮箱
  await page.fill('[data-testid="email-input"]', '<EMAIL>');
  
  // 点击发送验证邮件
  await page.click('[data-testid="send-verification-btn"]');
  
  // 验证成功消息
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
});
```

通过这个测试指南，你可以确保注册流程的每个步骤都正常工作，并且能够处理各种边缘情况。
