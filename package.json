{"name": "kyoto-restaurant-start", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:normal": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.808.0", "@aws-sdk/s3-request-presigner": "^3.808.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "framer-motion": "^12.11.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-intl": "^4.0.3", "openai": "^4.98.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}