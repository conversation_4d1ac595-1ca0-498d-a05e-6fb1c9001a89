# 厨房API权限统一更新计划

## 需要更新的文件列表

基于代码检索结果，以下API文件需要更新：

### 已更新 ✅
1. `src/app/api/kitchen/orders/route.ts` - 已更新为使用用户餐厅ID
2. `src/app/api/kitchen/dishes/route.ts` - 已更新为使用统一权限系统

### 待更新 🔄
1. `src/app/api/kitchen/dishes/[id]/route.ts`
2. `src/app/api/kitchen/order-items/update-status/route.ts`
3. `src/app/api/socket/route.ts`
4. `src/app/api/kitchen/manual-order/route.ts`
5. `src/app/api/kitchen/stats/route.ts`
6. `src/app/api/kitchen/categories/[id]/route.ts`
7. `src/app/api/kitchen/categories/route.ts`
8. `src/app/api/kitchen/orders/update-status/route.ts`
9. `src/app/api/upload/presigned-url/route.ts`
10. `src/app/api/kitchen/dishes/reorder/route.ts`
11. `src/app/api/kitchen/orders/history/route.ts`
12. `src/app/api/kitchen/order-items/update-quantity/route.ts`
13. `src/app/api/kitchen/categories/reorder/route.ts`
14. `src/app/api/tables/route.ts` - 特殊处理，有复杂的权限逻辑

## 更新模式

### 旧模式（需要移除）
```typescript
// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  const { data, error: userError } = await supabase.auth.getUser();
  // ... 重复的权限检查代码
}

export async function GET/POST(request: NextRequest) {
  const supabase = await createClient();
  const hasAccess = await validateKitchenAccess(supabase);
  if (!hasAccess) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // ... 使用前端传递的 restaurantId
}
```

### 新模式（统一权限）
```typescript
import { requireKitchenAccess } from '@/lib/auth/permissions';

export async function GET/POST(request: NextRequest) {
  // 验证用户权限并获取用户信息
  const permissions = await requireKitchenAccess(request);
  
  if (permissions instanceof NextResponse) {
    return permissions; // 返回错误响应
  }

  // 使用当前用户的餐厅ID
  const restaurantId = permissions.restaurantId;
  
  // 创建 Supabase 客户端
  const supabase = await createClient();
  
  // ... 使用 restaurantId 进行数据查询
}
```

## 关键改进

1. **移除重复代码**：删除每个文件中的 `validateKitchenAccess` 函数
2. **统一权限检查**：使用 `requireKitchenAccess` 函数
3. **数据隔离**：使用用户的餐厅ID，而不是前端传递的参数
4. **安全性**：防止用户访问其他餐厅的数据

## 特殊情况

### tables/route.ts
这个文件有更复杂的权限逻辑，需要特殊处理：
- 支持多种角色（super_admin, admin, kitchen, restaurant_owner）
- 需要检查用户是否属于目标餐厅
- 保留现有的权限检查逻辑，但可以优化

### socket/route.ts
WebSocket相关的API，需要确保：
- 只推送用户所属餐厅的数据
- 维持实时更新的功能

## 测试要点

更新后需要测试：
1. 用户只能看到自己餐厅的数据
2. 无法通过修改前端参数访问其他餐厅数据
3. 权限检查正常工作
4. 功能保持完整

## 执行顺序

1. 先更新简单的GET API（orders, dishes, stats等）
2. 再更新POST/PUT API（创建、更新操作）
3. 最后处理复杂的API（tables, socket）
4. 全面测试数据隔离
