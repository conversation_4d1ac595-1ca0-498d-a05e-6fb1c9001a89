# 用户管理脚本

这个目录包含用于创建和管理用户的脚本。

## 前提条件

1. 确保已安装 Node.js
2. 安装依赖：`npm install dotenv @supabase/supabase-js`
3. 在项目根目录创建 `.env` 文件，并添加以下内容：

```
NEXT_PUBLIC_SUPABASE_URL=https://your-supabase-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
```

注意：`SUPABASE_SERVICE_KEY` 是服务密钥，可以在 Supabase 项目设置中找到。这个密钥具有完全访问权限，请妥善保管。

## 脚本说明

### 1. 创建超级管理员用户

超级管理员可以访问所有餐厅的数据和管理功能。

```bash
node create-admin-user.js <EMAIL> password
```

### 2. 创建餐厅管理员用户

餐厅管理员只能管理特定餐厅的数据。

```bash
node create-restaurant-admin.js <EMAIL> password 550e8400-e29b-41d4-a716-446655440000
```

### 3. 创建厨房用户

厨房用户只能访问特定餐厅的厨房管理功能。

```bash
node create-kitchen-user.js <EMAIL> password restaurant_id
```

### 4. 创建餐厅所有者

餐厅所有者可以创建和管理自己的餐厅。此脚本会同时创建餐厅和餐厅所有者用户。

```bash
node create-restaurant-owner.js <EMAIL> password "餐厅名称"
```

## 获取餐厅 ID

如果您不知道餐厅 ID，可以通过以下方式获取：

1. 登录 Supabase 管理控制台
2. 进入 Table Editor
3. 选择 `restaurants` 表
4. 查找您需要的餐厅，并复制其 `id` 字段的值

## 注意事项

- 这些脚本会自动确认用户的电子邮件，无需用户进行额外的验证步骤
- 创建用户后，他们可以立即使用提供的电子邮件和密码登录系统
- 出于安全考虑，建议用户首次登录后更改密码
