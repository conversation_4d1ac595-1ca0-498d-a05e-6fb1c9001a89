require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// 初始化 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('环境变量未设置。请确保 NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_KEY 已设置。');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createRestaurantOwner(email, password, restaurantName) {
  try {
    console.log(`正在创建餐厅 "${restaurantName}" 和餐厅所有者: ${email}`);

    // 1. 创建餐厅
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .insert({
        name: restaurantName,
        is_active: true
      })
      .select()
      .single();

    if (restaurantError) {
      throw restaurantError;
    }

    console.log(`餐厅创建成功，ID: ${restaurantData.id}`);

    // 2. 获取基础服务计划ID
    const { data: basicPlanData, error: planError } = await supabase
      .from('service_plans')
      .select('id')
      .eq('name', 'basic')
      .single();

    if (planError) {
      // 回滚：删除餐厅
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw planError;
    }

    // 3. 检查是否是前10家餐厅（永久免费）
    const { count, error: countError } = await supabase
      .from('restaurants')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      // 回滚：删除餐厅
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw countError;
    }

    const isFreeEligible = count !== null && count <= 10; // 包括刚创建的餐厅

    // 设置服务结束日期
    const serviceEndDate = new Date();
    if (isFreeEligible) {
      // 永久免费（设置为100年后）
      serviceEndDate.setFullYear(serviceEndDate.getFullYear() + 100);
      console.log('该餐厅是前10家之一，将获得永久免费使用权！');
    } else {
      // 90天免费试用
      serviceEndDate.setDate(serviceEndDate.getDate() + 90);
      console.log('该餐厅将获得90天免费试用期。')
    }

    const { error: serviceError } = await supabase
      .from('restaurant_services')
      .insert({
        restaurant_id: restaurantData.id,
        service_plan_id: basicPlanData.id,
        start_date: new Date().toISOString(),
        end_date: serviceEndDate.toISOString(),
        is_trial: true
      });

    if (serviceError) {
      // 回滚：删除餐厅
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw serviceError;
    }

    console.log(`服务订阅创建成功，有效期至: ${serviceEndDate.toISOString()}`);

    // 4. 创建 Auth 用户
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // 自动确认电子邮件
    });

    if (authError) {
      // 回滚：删除餐厅和服务订阅
      await supabase.from('restaurant_services').delete().eq('restaurant_id', restaurantData.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw authError;
    }

    console.log('Auth 用户创建成功');

    // 5. 获取餐厅所有者角色 ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'restaurant_owner')
      .single();

    if (roleError) {
      // 回滚：删除 Auth 用户、餐厅和服务订阅
      await supabase.auth.admin.deleteUser(authUser.user.id);
      await supabase.from('restaurant_services').delete().eq('restaurant_id', restaurantData.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw roleError;
    }

    const ownerRoleId = roleData.id;

    // 6. 创建用户记录
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authUser.user.id,
        email: email,
        name: `${restaurantName} 所有者`,
        role_id: ownerRoleId,
        restaurant_id: restaurantData.id,
      })
      .select()
      .single();

    if (userError) {
      // 回滚：删除 Auth 用户、餐厅和服务订阅
      await supabase.auth.admin.deleteUser(authUser.user.id);
      await supabase.from('restaurant_services').delete().eq('restaurant_id', restaurantData.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw userError;
    }

    console.log('用户记录创建成功');
    console.log('餐厅所有者创建完成！');
    console.log(`餐厅 ID: ${restaurantData.id}`);
    console.log(`用户 ID: ${userData.id}`);
    console.log(`Auth ID: ${authUser.user.id}`);
    console.log(`角色: 餐厅所有者`);
    console.log(`服务计划: 基础版（免费试用）`);
    console.log(`试用期结束日期: ${serviceEndDate.toISOString()}`);

    return {
      restaurantId: restaurantData.id,
      userId: userData.id,
      authId: authUser.user.id,
      servicePlan: 'basic',
      serviceEndDate: serviceEndDate.toISOString()
    };
  } catch (error) {
    console.error('创建餐厅所有者时出错:', error);
    throw error;
  }
}

// 从命令行参数获取信息
const args = process.argv.slice(2);
if (args.length < 3) {
  console.log('用法: node create-restaurant-owner.js <email> <password> <restaurant_name>');
  process.exit(1);
}

const email = args[0];
const password = args[1];
const restaurantName = args[2];

createRestaurantOwner(email, password, restaurantName)
  .then(() => {
    console.log('操作完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('操作失败:', error);
    process.exit(1);
  });
