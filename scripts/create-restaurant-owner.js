require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// 初始化 Supabase 客户端
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('环境变量未设置。请确保 NEXT_PUBLIC_SUPABASE_URL 和 SUPABASE_SERVICE_KEY 已设置。');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createRestaurantOwner(email, password, restaurantName) {
  try {
    console.log(`正在创建餐厅 "${restaurantName}" 和餐厅所有者: ${email}`);
    
    // 1. 创建餐厅
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .insert({
        name: restaurantName,
        is_active: true,
        free_trial: true,
        free_trial_end_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() // 90 days from now
      })
      .select()
      .single();
    
    if (restaurantError) {
      throw restaurantError;
    }
    
    console.log(`餐厅创建成功，ID: ${restaurantData.id}`);
    
    // 2. 创建 Auth 用户
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // 自动确认电子邮件
    });
    
    if (authError) {
      // 回滚：删除餐厅
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw authError;
    }
    
    console.log('Auth 用户创建成功');
    
    // 3. 获取餐厅所有者角色 ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'restaurant_owner')
      .single();
    
    if (roleError) {
      // 回滚：删除 Auth 用户和餐厅
      await supabase.auth.admin.deleteUser(authUser.user.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw roleError;
    }
    
    const ownerRoleId = roleData.id;
    
    // 4. 创建用户记录
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authUser.user.id,
        email: email,
        name: `${restaurantName} 所有者`,
        role_id: ownerRoleId,
        restaurant_id: restaurantData.id,
      })
      .select()
      .single();
    
    if (userError) {
      // 回滚：删除 Auth 用户和餐厅
      await supabase.auth.admin.deleteUser(authUser.user.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      throw userError;
    }
    
    console.log('用户记录创建成功');
    console.log('餐厅所有者创建完成！');
    console.log(`餐厅 ID: ${restaurantData.id}`);
    console.log(`用户 ID: ${userData.id}`);
    console.log(`Auth ID: ${authUser.user.id}`);
    console.log(`角色: 餐厅所有者`);
    
    return {
      restaurantId: restaurantData.id,
      userId: userData.id,
      authId: authUser.user.id
    };
  } catch (error) {
    console.error('创建餐厅所有者时出错:', error);
    throw error;
  }
}

// 从命令行参数获取信息
const args = process.argv.slice(2);
if (args.length < 3) {
  console.log('用法: node create-restaurant-owner.js <email> <password> <restaurant_name>');
  process.exit(1);
}

const email = args[0];
const password = args[1];
const restaurantName = args[2];

createRestaurantOwner(email, password, restaurantName)
  .then(() => {
    console.log('操作完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('操作失败:', error);
    process.exit(1);
  });
