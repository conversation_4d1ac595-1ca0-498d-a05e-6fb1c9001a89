/**
 * 测试注册流程的脚本
 * 用于验证邮箱验证和注册完成的API端点
 */

const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

async function testSendVerification(email, language = 'en') {
  console.log(`\n🔄 测试发送验证邮件: ${email}`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/send-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        language,
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 验证邮件发送成功:', data.message);
      return true;
    } else {
      console.log('❌ 验证邮件发送失败:', data.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
    return false;
  }
}

async function testCompleteRegistration(email, restaurantName, password) {
  console.log(`\n🔄 测试完成注册: ${email} - ${restaurantName}`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/complete-registration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        restaurantName,
        password,
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 注册完成成功:');
      console.log('   餐厅:', data.restaurant);
      console.log('   服务:', data.service);
      return true;
    } else {
      console.log('❌ 注册完成失败:', data.error);
      return false;
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
    return false;
  }
}

async function testVerifyToken(tokenHash, type) {
  console.log(`\n🔄 测试验证令牌`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token_hash: tokenHash,
        type,
      }),
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 令牌验证成功:', data.email);
      return data.email;
    } else {
      console.log('❌ 令牌验证失败:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ 网络错误:', error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试注册流程...');
  console.log(`📍 测试环境: ${BASE_URL}`);
  
  // 测试数据
  const testEmail = `test-${Date.now()}@example.com`;
  const testRestaurant = `测试餐厅 ${Date.now()}`;
  const testPassword = 'TestPassword123!';
  
  console.log(`\n📧 测试邮箱: ${testEmail}`);
  console.log(`🏪 测试餐厅: ${testRestaurant}`);
  
  // 步骤 1: 发送验证邮件
  const emailSent = await testSendVerification(testEmail, 'zh');
  
  if (!emailSent) {
    console.log('\n❌ 测试失败: 无法发送验证邮件');
    return;
  }
  
  console.log('\n⏳ 请检查邮箱并手动点击验证链接...');
  console.log('   验证完成后，可以测试完成注册API');
  
  // 注意: 在实际测试中，需要手动验证邮箱后才能继续
  console.log('\n📝 手动测试完成注册API:');
  console.log(`   curl -X POST ${BASE_URL}/api/auth/complete-registration \\`);
  console.log(`     -H "Content-Type: application/json" \\`);
  console.log(`     -d '{"email":"${testEmail}","restaurantName":"${testRestaurant}","password":"${testPassword}"}'`);
  
  console.log('\n✅ 测试脚本完成');
  console.log('💡 提示: 这个脚本只能测试API端点，完整的邮箱验证需要手动操作');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testSendVerification,
  testCompleteRegistration,
  testVerifyToken,
};
