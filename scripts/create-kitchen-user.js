// 创建厨房用户的脚本
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// 创建 Supabase 客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY // 注意：这里使用服务密钥，而不是匿名密钥
);

async function createKitchenUser() {
  try {
    // 1. 获取参数
    const email = process.argv[2];
    const password = process.argv[3];
    const restaurantId = process.argv[4];
    
    if (!email || !password || !restaurantId) {
      console.error('请提供电子邮件、密码和餐厅 ID 作为参数');
      console.error('用法: node create-kitchen-user.js <EMAIL> password restaurant_id');
      process.exit(1);
    }
    
    // 2. 验证餐厅是否存在
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .select('id, name')
      .eq('id', restaurantId)
      .single();
    
    if (restaurantError) {
      throw new Error(`餐厅不存在: ${restaurantError.message}`);
    }
    
    console.log(`正在为餐厅 "${restaurantData.name}" 创建厨房用户: ${email}`);
    
    // 3. 创建 Auth 用户
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // 自动确认电子邮件
    });
    
    if (authError) {
      throw authError;
    }
    
    console.log('Auth 用户创建成功');
    
    // 4. 获取厨房角色 ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'kitchen')
      .single();
    
    if (roleError) {
      throw roleError;
    }
    
    const kitchenRoleId = roleData.id;
    
    // 5. 创建用户记录
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authUser.user.id,
        email: email,
        name: `${restaurantData.name} 厨房`,
        role_id: kitchenRoleId,
        restaurant_id: restaurantId,
      })
      .select()
      .single();
    
    if (userError) {
      throw userError;
    }
    
    console.log('用户记录创建成功');
    console.log('厨房用户创建完成！');
    
  } catch (error) {
    console.error('创建厨房用户时出错:', error);
  } finally {
    process.exit(0);
  }
}

createKitchenUser();
