// 创建管理员用户的脚本
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// 创建 Supabase 客户端
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY // 注意：这里使用服务密钥，而不是匿名密钥
);

async function createAdminUser() {
  try {
    // 1. 创建 Auth 用户
    const email = process.argv[2];
    const password = process.argv[3];
    
    if (!email || !password) {
      console.error('请提供电子邮件和密码作为参数');
      console.error('用法: node create-admin-user.js <EMAIL> password');
      process.exit(1);
    }
    
    console.log(`正在创建管理员用户: ${email}`);
    
    // 创建用户
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // 自动确认电子邮件
    });
    
    if (authError) {
      throw authError;
    }
    
    console.log('Auth 用户创建成功');
    
    // 2. 获取超级管理员角色 ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'super_admin')
      .single();
    
    if (roleError) {
      throw roleError;
    }
    
    const superAdminRoleId = roleData.id;
    
    // 3. 创建用户记录
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authUser.user.id,
        email: email,
        name: '超级管理员',
        role_id: superAdminRoleId,
      })
      .select()
      .single();
    
    if (userError) {
      throw userError;
    }
    
    console.log('用户记录创建成功');
    console.log('超级管理员用户创建完成！');
    
  } catch (error) {
    console.error('创建管理员用户时出错:', error);
  } finally {
    process.exit(0);
  }
}

createAdminUser();
