import createNextIntlPlugin from 'next-intl/plugin';
import type { NextConfig } from "next";

// Explicitly specify the path for i18n configuration
const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**', // 开发环境
      },
      {
        protocol: 'https',
        hostname: '*.freepik.com', // 开发环境
      },
    ],
  },
};

export default withNextIntl(nextConfig);
