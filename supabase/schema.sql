-- Create restaurants table
CREATE TABLE restaurants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tables table
CREATE TABLE tables (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  table_number TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(restaurant_id, table_number)
);

-- Create categories table
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_ko TEXT NOT NULL,
  name_zh TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dishes table
CREATE TABLE dishes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  name_en TEXT NOT NULL,
  name_ja TEXT NOT NULL,
  name_ko TEXT NOT NULL,
  name_zh TEXT NOT NULL,
  description_en TEXT,
  description_ja TEXT,
  description_ko TEXT,
  description_zh TEXT,
  price DECIMAL(10, 2) NOT NULL,
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  is_popular BOOLEAN DEFAULT FALSE,
  is_special BOOLEAN DEFAULT FALSE,
  is_discounted BOOLEAN DEFAULT FALSE,
  discount_percentage DECIMAL(5, 2) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dish_categories junction table
CREATE TABLE dish_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  dish_id UUID NOT NULL REFERENCES dishes(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dish_id, category_id)
);

-- Create orders table
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  table_id UUID NOT NULL REFERENCES tables(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create order_items table
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  dish_id UUID NOT NULL REFERENCES dishes(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create auth_tokens table for security
CREATE TABLE auth_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  token TEXT NOT NULL,
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  table_id UUID NOT NULL REFERENCES tables(id) ON DELETE CASCADE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(token)
);

-- Create RLS policies
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tables ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE dishes ENABLE ROW LEVEL SECURITY;
ALTER TABLE dish_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (read-only)
CREATE POLICY "Public can view restaurants" ON restaurants FOR SELECT USING (true);
CREATE POLICY "Public can view tables" ON tables FOR SELECT USING (true);
CREATE POLICY "Public can view categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Public can view dishes" ON dishes FOR SELECT USING (true);
CREATE POLICY "Public can view dish_categories" ON dish_categories FOR SELECT USING (true);
CREATE POLICY "Public can view orders" ON orders FOR SELECT USING (true);
CREATE POLICY "Public can view order_items" ON order_items FOR SELECT USING (true);

-- Create policies for public access (write)
CREATE POLICY "Public can insert orders" ON orders FOR INSERT WITH CHECK (true);
CREATE POLICY "Public can update orders" ON orders FOR UPDATE USING (true);
CREATE POLICY "Public can insert order_items" ON order_items FOR INSERT WITH CHECK (true);
CREATE POLICY "Public can update order_items" ON order_items FOR UPDATE USING (true);

-- Create functions for translation
CREATE OR REPLACE FUNCTION translate_text(
  text_to_translate TEXT,
  source_language TEXT,
  target_language TEXT
) RETURNS TEXT AS $$
BEGIN
  -- In a real implementation, this would call an external translation API
  -- For now, we'll just return the original text
  RETURN text_to_translate;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically translate dish and category names/descriptions
CREATE OR REPLACE FUNCTION auto_translate_dish() RETURNS TRIGGER AS $$
BEGIN
  -- If Japanese text is provided but others are not, translate from Japanese
  IF NEW.name_ja IS NOT NULL AND (NEW.name_en IS NULL OR NEW.name_ko IS NULL OR NEW.name_zh IS NULL) THEN
    -- In a real implementation, these would call external translation APIs
    NEW.name_en := COALESCE(NEW.name_en, translate_text(NEW.name_ja, 'ja', 'en'));
    NEW.name_ko := COALESCE(NEW.name_ko, translate_text(NEW.name_ja, 'ja', 'ko'));
    NEW.name_zh := COALESCE(NEW.name_zh, translate_text(NEW.name_ja, 'ja', 'zh'));
  END IF;

  IF NEW.description_ja IS NOT NULL AND (NEW.description_en IS NULL OR NEW.description_ko IS NULL OR NEW.description_zh IS NULL) THEN
    NEW.description_en := COALESCE(NEW.description_en, translate_text(NEW.description_ja, 'ja', 'en'));
    NEW.description_ko := COALESCE(NEW.description_ko, translate_text(NEW.description_ja, 'ja', 'ko'));
    NEW.description_zh := COALESCE(NEW.description_zh, translate_text(NEW.description_ja, 'ja', 'zh'));
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION auto_translate_category() RETURNS TRIGGER AS $$
BEGIN
  -- If Japanese text is provided but others are not, translate from Japanese
  IF NEW.name_ja IS NOT NULL AND (NEW.name_en IS NULL OR NEW.name_ko IS NULL OR NEW.name_zh IS NULL) THEN
    -- In a real implementation, these would call external translation APIs
    NEW.name_en := COALESCE(NEW.name_en, translate_text(NEW.name_ja, 'ja', 'en'));
    NEW.name_ko := COALESCE(NEW.name_ko, translate_text(NEW.name_ja, 'ja', 'ko'));
    NEW.name_zh := COALESCE(NEW.name_zh, translate_text(NEW.name_ja, 'ja', 'zh'));
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for auto-translation
CREATE TRIGGER dish_auto_translate
BEFORE INSERT OR UPDATE ON dishes
FOR EACH ROW EXECUTE FUNCTION auto_translate_dish();

CREATE TRIGGER category_auto_translate
BEFORE INSERT OR UPDATE ON categories
FOR EACH ROW EXECUTE FUNCTION auto_translate_category();

-- Create indexes for better performance
CREATE INDEX idx_dishes_restaurant_id ON dishes(restaurant_id);
CREATE INDEX idx_categories_restaurant_id ON categories(restaurant_id);
CREATE INDEX idx_tables_restaurant_id ON tables(restaurant_id);
CREATE INDEX idx_orders_restaurant_id ON orders(restaurant_id);
CREATE INDEX idx_orders_table_id ON orders(table_id);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_dish_categories_dish_id ON dish_categories(dish_id);
CREATE INDEX idx_dish_categories_category_id ON dish_categories(category_id);
