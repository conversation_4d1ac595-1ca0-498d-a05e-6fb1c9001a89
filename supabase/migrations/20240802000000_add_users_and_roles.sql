-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  auth_id UUID UNIQUE, -- 关联到 Supabase Auth 用户
  email TEXT NOT NULL UNIQUE,
  name TEXT,
  role_id UUID NOT NULL REFERENCES roles(id),
  restaurant_id UUID REFERENCES restaurants(id), -- 可以为空，表示超级管理员
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入基本角色
INSERT INTO roles (name, description) VALUES
  ('super_admin', 'Super administrator with access to all restaurants'),
  ('admin', 'Restaurant administrator'),
  ('kitchen', 'Kitchen staff');

-- 启用行级安全
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 创建策略
-- 角色表策略
CREATE POLICY "Authenticated users can view roles" ON roles
  FOR SELECT USING (auth.role() = 'authenticated');

-- 用户表策略
CREATE POLICY "Users can view their own record" ON users
  FOR SELECT USING (auth.uid() = auth_id);

CREATE POLICY "Super admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'super_admin'
      )
    )
  );

CREATE POLICY "Restaurant admins can view users in their restaurant" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'admin'
      )
      AND users.restaurant_id = restaurant_id
    )
  );

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_auth_id ON users(auth_id);
CREATE INDEX IF NOT EXISTS idx_users_role_id ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_users_restaurant_id ON users(restaurant_id);
