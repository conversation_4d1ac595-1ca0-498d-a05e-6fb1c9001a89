-- 向orders表添加支付相关字段
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_method TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status TEXT;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_time TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS total_amount DECIMAL(10, 2);

-- 创建order_analytics表，用于存储订单分析数据
CREATE TABLE IF NOT EXISTS order_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  table_id UUID NOT NULL REFERENCES tables(id) ON DELETE CASCADE,
  table_number TEXT NOT NULL,
  total_amount DECIMAL(10, 2) NOT NULL,
  items_count INTEGER NOT NULL,
  payment_method TEXT,
  payment_time TIMESTAMP WITH TIME ZONE,
  order_time TIMESTAMP WITH TIME ZONE,
  day_of_week INTEGER, -- 0-6，表示星期几
  hour_of_day INTEGER, -- 0-23，表示小时
  items_data JSONB, -- 存储订单项目的详细信息
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_order_analytics_restaurant_id ON order_analytics(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_order_analytics_order_time ON order_analytics(order_time);
CREATE INDEX IF NOT EXISTS idx_order_analytics_payment_time ON order_analytics(payment_time);
CREATE INDEX IF NOT EXISTS idx_order_analytics_day_of_week ON order_analytics(day_of_week);
CREATE INDEX IF NOT EXISTS idx_order_analytics_hour_of_day ON order_analytics(hour_of_day);

-- 设置RLS策略
ALTER TABLE order_analytics ENABLE ROW LEVEL SECURITY;

-- 创建公共访问策略
CREATE POLICY "Public can view order_analytics" ON order_analytics FOR SELECT USING (true);
CREATE POLICY "Public can insert order_analytics" ON order_analytics FOR INSERT WITH CHECK (true);
