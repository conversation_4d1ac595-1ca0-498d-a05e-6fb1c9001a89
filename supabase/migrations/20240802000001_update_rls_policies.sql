-- 删除现有的过于宽松的策略
DROP POLICY IF EXISTS "Public can view restaurants" ON restaurants;
DROP POLICY IF EXISTS "Public can view tables" ON tables;
DROP POLICY IF EXISTS "Public can view categories" ON categories;
DROP POLICY IF EXISTS "Public can view dishes" ON dishes;
DROP POLICY IF EXISTS "Public can view dish_categories" ON dish_categories;
DROP POLICY IF EXISTS "Public can view orders" ON orders;
DROP POLICY IF EXISTS "Public can view order_items" ON order_items;
DROP POLICY IF EXISTS "Public can insert orders" ON orders;
DROP POLICY IF EXISTS "Public can update orders" ON orders;
DROP POLICY IF EXISTS "Public can insert order_items" ON order_items;
DROP POLICY IF EXISTS "Public can update order_items" ON order_items;
DROP POLICY IF EXISTS "Public can view order_analytics" ON order_analytics;
DROP POLICY IF EXISTS "Public can insert order_analytics" ON order_analytics;

-- 创建更安全的策略

-- 餐厅表策略
CREATE POLICY "Authenticated users can view restaurants" ON restaurants
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Super admins can manage restaurants" ON restaurants
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'super_admin'
      )
    )
  );

CREATE POLICY "Restaurant admins can manage their restaurant" ON restaurants
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'admin'
      )
      AND users.restaurant_id = id
    )
  );

-- 桌子表策略
CREATE POLICY "Authenticated users can view tables" ON tables
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage tables" ON tables
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

-- 类别表策略
CREATE POLICY "Authenticated users can view categories" ON categories
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage categories" ON categories
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

-- 菜品表策略
CREATE POLICY "Authenticated users can view dishes" ON dishes
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage dishes" ON dishes
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

-- 菜品类别关联表策略
CREATE POLICY "Authenticated users can view dish_categories" ON dish_categories
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage dish_categories" ON dish_categories
  USING (
    EXISTS (
      SELECT 1 FROM users u
      JOIN dishes d ON u.restaurant_id = d.restaurant_id
      WHERE u.auth_id = auth.uid()
      AND d.id = dish_id
    )
  );

-- 订单表策略
CREATE POLICY "Authenticated users can view orders" ON orders
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage orders" ON orders
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

-- 订单项目表策略
CREATE POLICY "Authenticated users can view order_items" ON order_items
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage order_items" ON order_items
  USING (
    EXISTS (
      SELECT 1 FROM users u
      JOIN orders o ON u.restaurant_id = o.restaurant_id
      WHERE u.auth_id = auth.uid()
      AND o.id = order_id
    )
  );

-- 订单分析表策略
CREATE POLICY "Authenticated users can view order_analytics" ON order_analytics
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Restaurant staff can manage order_analytics" ON order_analytics
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

-- 为客户端访问创建特殊策略（通过令牌验证）
CREATE POLICY "Customers with valid token can view restaurant data" ON restaurants
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth_tokens
      WHERE auth_tokens.restaurant_id = id
      AND auth_tokens.token = current_setting('app.current_token', true)
      AND auth_tokens.expires_at > NOW()
    )
  );

CREATE POLICY "Customers with valid token can view and manage orders" ON orders
  USING (
    EXISTS (
      SELECT 1 FROM auth_tokens
      WHERE auth_tokens.restaurant_id = restaurant_id
      AND auth_tokens.token = current_setting('app.current_token', true)
      AND auth_tokens.expires_at > NOW()
    )
  );

CREATE POLICY "Customers with valid token can view and manage order items" ON order_items
  USING (
    EXISTS (
      SELECT 1 FROM auth_tokens at
      JOIN orders o ON at.order_id = o.id
      WHERE o.id = order_id
      AND at.token = current_setting('app.current_token', true)
      AND at.expires_at > NOW()
    )
  );
