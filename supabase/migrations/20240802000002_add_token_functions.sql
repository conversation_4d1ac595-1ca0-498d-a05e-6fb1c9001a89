-- 创建设置令牌的函数
CREATE OR REPLACE FUNCTION set_auth_token(token TEXT) RETURNS VOID AS $$
BEGIN
  PERFORM set_config('app.current_token', token, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建验证令牌的函数
CREATE OR REPLACE FUNCTION verify_auth_token(token TEXT, restaurant_id UUID, table_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  token_valid BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM auth_tokens
    WHERE auth_tokens.token = token
    AND auth_tokens.restaurant_id = verify_auth_token.restaurant_id
    AND auth_tokens.table_id = verify_auth_token.table_id
    AND auth_tokens.expires_at > NOW()
  ) INTO token_valid;
  
  RETURN token_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
