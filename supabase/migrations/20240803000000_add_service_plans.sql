-- Add active status to restaurants table
ALTER TABLE restaurants 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Create service plans table
CREATE TABLE IF NOT EXISTS service_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create restaurant services table to track which services a restaurant has
CREATE TABLE IF NOT EXISTS restaurant_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  restaurant_id UUID NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  service_plan_id UUID NOT NULL REFERENCES service_plans(id),
  start_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  is_trial BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert basic service plans
INSERT INTO service_plans (name, description) VALUES
  ('basic', 'Basic QR code ordering with kitchen management'),
  ('plus', 'Basic features plus electronic payment'),
  ('premium', 'All features including loyalty program');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_restaurant_services_restaurant_id ON restaurant_services(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_services_service_plan_id ON restaurant_services(service_plan_id);
CREATE INDEX IF NOT EXISTS idx_restaurant_services_end_date ON restaurant_services(end_date);

-- Enable RLS on new tables
ALTER TABLE service_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE restaurant_services ENABLE ROW LEVEL SECURITY;

-- Create policies for service_plans
CREATE POLICY "Anyone can view service plans" ON service_plans
  FOR SELECT USING (true);

-- Create policies for restaurant_services
CREATE POLICY "Restaurant owners can view their services" ON restaurant_services
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND users.restaurant_id = restaurant_id
    )
  );

CREATE POLICY "Super admins can manage all restaurant services" ON restaurant_services
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'super_admin'
      )
    )
  );

-- Add restaurant_owner role
INSERT INTO roles (name, description)
VALUES ('restaurant_owner', 'Restaurant owner with access to their restaurant')
ON CONFLICT (name) DO NOTHING;

-- Create policy for restaurant owners
CREATE POLICY "Restaurant owners can manage their restaurant" ON restaurants
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = id
    )
  );

-- Create policy for restaurant owners to manage tables
CREATE POLICY "Restaurant owners can manage tables" ON tables
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = restaurant_id
    )
  );

-- Create policy for restaurant owners to manage categories
CREATE POLICY "Restaurant owners can manage categories" ON categories
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = restaurant_id
    )
  );

-- Create policy for restaurant owners to manage dishes
CREATE POLICY "Restaurant owners can manage dishes" ON dishes
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = restaurant_id
    )
  );

-- Create policy for restaurant owners to manage dish_categories
CREATE POLICY "Restaurant owners can manage dish_categories" ON dish_categories
  USING (
    EXISTS (
      SELECT 1 FROM users u
      JOIN dishes d ON u.restaurant_id = d.restaurant_id
      WHERE u.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = u.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND d.id = dish_id
    )
  );

-- Create policy for restaurant owners to manage orders
CREATE POLICY "Restaurant owners can manage orders" ON orders
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = restaurant_id
    )
  );

-- Create policy for restaurant owners to manage order_items
CREATE POLICY "Restaurant owners can manage order_items" ON order_items
  USING (
    EXISTS (
      SELECT 1 FROM users u
      JOIN orders o ON u.restaurant_id = o.restaurant_id
      WHERE u.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = u.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND o.id = order_id
    )
  );

-- Create policy for restaurant owners to manage order_analytics
CREATE POLICY "Restaurant owners can manage order_analytics" ON order_analytics
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.auth_id = auth.uid()
      AND EXISTS (
        SELECT 1 FROM roles
        WHERE roles.id = users.role_id
        AND roles.name = 'restaurant_owner'
      )
      AND users.restaurant_id = restaurant_id
    )
  );
