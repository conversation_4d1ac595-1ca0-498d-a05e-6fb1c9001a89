FROM node:22-alpine AS base

# 安装构建必要的工具
RUN apk add --no-cache libc6-compat python3 make g++

# 安装依赖
FROM base AS deps
WORKDIR /app

# 复制 package.json 和 package-lock.json (如果可用)
COPY package.json package-lock.json* ./

# 安装依赖
RUN npm config set fetch-retry-maxtimeout 600000 && \
    npm config set fetch-retry-mintimeout 100000 && \
    npm ci --no-audit --prefer-offline --legacy-peer-deps || \
    npm install --no-audit --prefer-offline --legacy-peer-deps

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 确保所有依赖都正确安装
RUN npm config set fetch-retry-maxtimeout 600000 && \
    npm config set fetch-retry-mintimeout 100000 && \
    npm ci --no-audit --prefer-offline --legacy-peer-deps || \
    npm install --no-audit --prefer-offline --legacy-peer-deps

# 设置环境变量
ENV NEXT_TELEMETRY_DISABLED 1

# 设置 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV OPENAI_API_KEY="temporary-placeholder"
ENV NEXT_OPENAI_BASE_URL="temporary-placeholder"

# 构建应用
RUN NODE_ENV=production npm run build --verbose || (echo "Build failed. See error above." && exit 1)

# 生产环境
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

# 创建非 root 用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
USER nextjs

# 复制必要的文件
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 设置启动命令
CMD ["node", "server.js"]
