import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * 创建 Supabase 服务器端客户端
 * 使用 SUPABASE_URL 和 SUPABASE_ANON_KEY 环境变量（非公开）
 */
export async function createClient() {
  // cookies() 是异步的，需要 await
  const cookieStore = await cookies();

  // 使用非公开环境变量
  const supabaseUrl = process.env.NEXT_SUPABASE_URL || process.env.SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY!;

  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name: string) {
          // 使用同步方式获取cookie值
          const cookie = cookieStore.get(name);
          return cookie?.value;
        },
        set(name: string, value: string, options: any) {
          // 使用同步方式设置cookie
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          // 使用同步方式移除cookie
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );
}

/**
 * 创建 Supabase 服务器端管理客户端
 * 使用 SUPABASE_URL 和 SUPABASE_SERVICE_KEY 环境变量（非公开）
 * 此客户端具有管理权限，仅在需要管理操作时使用
 */
export async function createAdminClient() {
  // 使用非公开环境变量
  const supabaseUrl = process.env.NEXT_SUPABASE_URL || process.env.SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!;

  if (!supabaseServiceKey) {
    throw new Error('SUPABASE_SERVICE_KEY is not defined');
  }

  // 使用 createServerClient 而不是 createClient，因为不需要 cookie 管理
  return createServerClient(
    supabaseUrl,
    supabaseServiceKey,
    {
      cookies: {
        get(name: string) {
          return undefined; // 不使用 cookie
        },
        set(name: string, value: string, options: any) {
          return; // 不设置 cookie
        },
        remove(name: string, options: any) {
          return; // 不删除 cookie
        },
      },
    }
  );
}