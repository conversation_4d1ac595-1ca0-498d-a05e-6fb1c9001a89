import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// 创建 Supabase 客户端，添加更多配置以确保会话正确处理
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true, // 确保会话被持久化到 localStorage 和 cookie
    autoRefreshToken: true, // 自动刷新令牌
    detectSessionInUrl: true, // 检测 URL 中的会话
    flowType: 'pkce', // 使用 PKCE 流程，这是最安全的方式
  },
});
