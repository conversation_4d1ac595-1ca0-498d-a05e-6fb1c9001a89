import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type CartItem = {
  id: string;
  name: string;
  price: number;
  quantity: number;
  tableId: string;
  submitted?: boolean; // 标记是否已提交
  orderId?: string;   // 如果已提交，存储订单ID
  groupId?: string; // 添加时间戳,按submit时分组。new Date().getTime()
};

type CartStore = {
  items: CartItem[];
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemId: string, tableId: string, groupId?: string) => void;
  updateQuantity: (itemId: string, quantity: number, tableId: string, groupId?: string) => void;
  clearCart: (tableId: string) => void;
  clearAllCart: () => void; // 新增完全清空购物车的方法
  markAsSubmitted: (tableId: string, orderId: string) => void; // 新增标记已提交的功能
  getSubmittedItems: (tableId: string) => CartItem[]; // 获取已提交的商品
  getUnsubmittedItems: (tableId: string) => CartItem[]; // 获取未提交的商品
};

// 创建一个类型安全的store
export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],

      addToCart: (newItem) =>
        set((state) => {
          const existingItemIndex = state.items.findIndex(
            (item) => item.id === newItem.id && item.tableId === newItem.tableId && !item.submitted
          );

          if (existingItemIndex >= 0) {
            const updatedItems = [...state.items];
            updatedItems[existingItemIndex].quantity += newItem.quantity;
            return { items: updatedItems };
          } else {
            return { items: [...state.items, { ...newItem, submitted: false }] };
          }
        }),

      removeFromCart: (itemId, tableId) =>
        set((state) => ({
          items: state.items.filter(
            (item) => !(item.id === itemId && item.tableId === tableId && !item.submitted)
          ),
        })),

      updateQuantity: (itemId, quantity, tableId) =>
        set((state) => {
          if (quantity <= 0) {
            return {
              items: state.items.filter(
                (item) => !(item.id === itemId && item.tableId === tableId && !item.submitted)
              ),
            };
          }

          return {
            items: state.items.map((item) =>
              item.id === itemId && item.tableId === tableId && !item.submitted
                ? { ...item, quantity }
                : item
            ),
          };
        }),

      clearCart: (tableId) =>
        set((state) => ({
          items: state.items.filter((item) => item.tableId !== tableId),
        })),

      // 完全清空购物车
      clearAllCart: () =>
        set({ items: [] }),

      // 标记表格的未提交商品为已提交
      markAsSubmitted: (tableId, orderId) =>
        set((state) => ({
          items: state.items.map((item) =>{
            const currentTime = new Date();
            const groupId = `${currentTime.getHours()}:${currentTime.getMinutes()}:${currentTime.getSeconds()}`
            return item.tableId === tableId && !item.submitted
              ? { ...item, submitted: true, orderId, groupId: groupId }
              : item
          }
          ),
        })),

      // 获取表格的已提交商品
      getSubmittedItems: (tableId) => {
        return get().items.filter(
          (item) => item.tableId === tableId && item.submitted
        );
      },

      // 获取表格的未提交商品
      getUnsubmittedItems: (tableId) => {
        return get().items.filter(
          (item) => item.tableId === tableId && !item.submitted
        );
      },
    }),
    {
      name: 'cart-storage',
    }
  )
);
