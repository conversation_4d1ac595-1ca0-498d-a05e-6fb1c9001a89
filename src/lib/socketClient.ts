import { io, Socket } from 'socket.io-client';

// 单例模式，确保只有一个Socket.IO连接
let socket: Socket | null = null;

// 初始化Socket.IO客户端连接
export const initSocketConnection = async () => {
  if (socket && socket.connected) {
    return socket;
  }

  // 先调用API路由初始化Socket.IO服务器
  try {
    await fetch('/api/socket');
  } catch (error: any) {
    console.error('Error initializing Socket.IO server:', error);
  }

  // 创建Socket.IO客户端连接
  socket = io({
    path: '/api/socket',
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    autoConnect: true,
  });

  // 设置连接事件
  socket.on('connect', () => {
    console.log('Connected to Socket.IO server');
  });

  socket.on('connect_error', (error) => {
    console.error('Socket.IO connection error:', error);
  });

  socket.on('disconnect', (reason) => {
    console.log('Disconnected from Socket.IO server:', reason);
  });

  return socket;
};

// 获取Socket.IO客户端连接
export const getSocket = () => {
  if (!socket) {
    throw new Error('Socket.IO client not initialized');
  }
  return socket;
};

// 关闭Socket.IO客户端连接
export const closeSocketConnection = () => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};
