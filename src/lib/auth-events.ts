/**
 * 认证状态变化事件工具
 * 用于在登录/登出时通知PublicHeader组件更新状态
 */

export const triggerAuthStateChange = () => {
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('authStateChange');
    window.dispatchEvent(event);
  }
};

export const setAuthData = (restaurantId: string, authToken?: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('restaurant_id', restaurantId);
    if (authToken) {
      localStorage.setItem('auth_token', authToken);
    }
    triggerAuthStateChange();
  }
};

export const clearAuthData = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('restaurant_id');
    localStorage.removeItem('auth_token');
    triggerAuthStateChange();
  }
};

export const getAuthData = () => {
  if (typeof window !== 'undefined') {
    return {
      restaurantId: localStorage.getItem('restaurant_id'),
      authToken: localStorage.getItem('auth_token'),
    };
  }
  return {
    restaurantId: null,
    authToken: null,
  };
};

export const isAuthenticated = () => {
  const { restaurantId, authToken } = getAuthData();
  return !!(restaurantId || authToken);
};
