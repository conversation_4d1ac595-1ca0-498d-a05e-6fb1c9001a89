import OpenAI from 'openai';

// 创建 OpenAI 客户端
const openai = new OpenAI({
  apiKey: process.env.NEXT_OPENAI_API_KEY,
  baseURL: process.env.NEXT_OPENAI_BASE_URL || 'https://api.openai.com/v1',
});

// 翻译类别名称
export async function translateCategoryName(name: string, sourceLanguage: 'zh' | 'ja') {
  // 检查是否有 API 密钥
  if (!process.env.NEXT_OPENAI_API_KEY || process.env.NEXT_OPENAI_API_KEY === 'sk-your-api-key') {
    console.warn('OpenAI API key not configured. Using fallback translation.');
    return {
      en: name,
      zh: name,
      ja: name,
      ko: name
    };
  }

  try {
    // 构建提示词
    const prompt = `
你是一个专业的多语言美食翻译专家。你的任务是将用户输入的${sourceLanguage === 'zh' ? '中文' : '日文'}的菜品类别原始名称翻译成其他语言。
请确保翻译符合各国的文化习惯和常见表达方式。

请提供以下语言的翻译:
- 英文 (en)
- ${sourceLanguage === 'zh' ? '日文 (ja)' : '中文 (zh)'}
- 韩文 (ko)

重要：
1. "Respond ONLY with a valid JSON object."
2. "Do not include any conversational fluff or explanations outside the JSON itself."
3. "Your entire response must be parsable by JSON.parse()."
4. JSON格式必须严格如下:
{
  "en": "英文翻译",
  "zh": "中文翻译",
  "ja": "日文翻译",
  "ko": "韩文翻译"
}

注意：对于源语言，请在结果中包含原始名称。
`;
    const userPrompt= `原始名称 (${sourceLanguage === 'zh' ? '中文' : '日文'}): "${name}"`

    // 调用 OpenAI API
    const response = await openai.chat.completions.create({
      model: process.env.NEXT_OPENAI_MODEL || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: prompt
        },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 500,
      response_format: { type: 'json_object' }
    });

    // 解析响应
    const content = response.choices[0]?.message?.content || '';

    console.log('Translation API response for:', name);
    console.log('Raw content:', content);

    try {
      // 尝试解析 JSON
      const translations = JSON.parse(content);

      console.log('Parsed translations:', translations);

      // 确保所有语言都有翻译
      const result = {
        en: translations.en || name,
        zh: translations.zh || name,
        ja: translations.ja || name,
        ko: translations.ko || name
      };

      // 确保源语言保持原始名称
      if (sourceLanguage === 'zh') {
        result.zh = name;
      } else if (sourceLanguage === 'ja') {
        result.ja = name;
      }

      console.log('Final translation result:', result);
      return result;
    } catch (parseError) {
      console.error('Error parsing translation response:', parseError);
      if (parseError instanceof Error) {
        console.error('Parse error details:', parseError.message);
      }
      console.log('Raw response content:', content);

      // 尝试清理内容并重新解析
      try {
        // 尝试提取 JSON 部分
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonContent = jsonMatch[0];
          console.log('Extracted JSON content:', jsonContent);

          const extractedTranslations = JSON.parse(jsonContent);
          console.log('Parsed extracted JSON:', extractedTranslations);

          const result = {
            en: extractedTranslations.en || name,
            zh: extractedTranslations.zh || name,
            ja: extractedTranslations.ja || name,
            ko: extractedTranslations.ko || name
          };

          // 确保源语言保持原始名称
          if (sourceLanguage === 'zh') {
            result.zh = name;
          } else if (sourceLanguage === 'ja') {
            result.ja = name;
          }

          console.log('Final translation result (after extraction):', result);
          return result;
        }
      } catch (extractError) {
        console.error('Failed to extract and parse JSON:', extractError);
      }

      // 返回默认值
      const defaultResult = {
        en: name,
        zh: name,
        ja: name,
        ko: name
      };

      console.log('Using default result:', defaultResult);
      return defaultResult;
    }
  } catch (error: any) {
    console.error('Translation error:', error);

    // 检查是否是配额错误
    if (error instanceof Error && error.message.includes('429')) {
      console.warn('OpenAI API quota exceeded. Using fallback translation.');
    } else if (error instanceof Error && error.message.includes('401')) {
      console.warn('OpenAI API authentication failed. Using fallback translation.');
    } else {
      console.warn('OpenAI API error. Using fallback translation.');
    }

    // 返回默认值
    return {
      en: name,
      zh: name,
      ja: name,
      ko: name
    };
  }
}

// 翻译菜品名称和描述
export async function translateDish(
  name: string,
  description: string,
  sourceLanguage: 'zh' | 'ja'
) {
  // 检查是否有 API 密钥
  if (!process.env.NEXT_OPENAI_API_KEY || process.env.NEXT_OPENAI_API_KEY === 'sk-your-api-key') {
    console.warn('OpenAI API key not configured. Using fallback translation.');
    return {
      name: {
        en: name,
        zh: name,
        ja: name,
        ko: name
      },
      description: {
        en: description,
        zh: description,
        ja: description,
        ko: description
      }
    };
  }

  try {
    // 构建提示词
    const prompt = `
你是一个专业的多语言美食翻译专家。你的任务是将以下${sourceLanguage === 'zh' ? '中文' : '日文'}的原始菜品名称和描述翻译成其他语言。
请确保翻译符合各国的文化习惯和常见表达方式。

请提供以下语言的翻译:
- 英文 (en)
- ${sourceLanguage === 'zh' ? '日文 (ja)' : '中文 (zh)'}
- 韩文 (ko)

重要：你必须只返回一个有效的JSON对象，不要包含任何其他文本、解释或前缀。
JSON格式必须严格如下:
{
  "name": {
    "en": "英文名称翻译",
    "zh": "中文名称翻译",
    "ja": "日文名称翻译",
    "ko": "韩文名称翻译"
  },
  "description": {
    "en": "英文描述翻译",
    "zh": "中文描述翻译",
    "ja": "日文描述翻译",
    "ko": "韩文描述翻译"
  }
}

注意：对于源语言，请在结果中包含原始名称和描述。
`;
    const userPrompt = `
原始名称 (${sourceLanguage === 'zh' ? '中文' : '日文'}): "${name}"
原始描述 (${sourceLanguage === 'zh' ? '中文' : '日文'}): "${description}"
`
    // 调用 OpenAI API
    const response = await openai.chat.completions.create({
      model: process.env.NEXT_OPENAI_MODEL || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: prompt
        },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
      max_tokens: 2000,
      response_format: { type: 'json_object' }
    });

    // 解析响应
    const content = response.choices[0]?.message?.content || '';

    console.log('Translation API response for dish:', name);
    console.log('Raw content:', content);

    try {
      // 尝试解析 JSON
      const translations = JSON.parse(content);

      console.log('Parsed dish translations:', translations);

      // 确保所有语言都有翻译
      const result = {
        name: {
          en: translations.name?.en || name,
          zh: translations.name?.zh || name,
          ja: translations.name?.ja || name,
          ko: translations.name?.ko || name
        },
        description: {
          en: translations.description?.en || description,
          zh: translations.description?.zh || description,
          ja: translations.description?.ja || description,
          ko: translations.description?.ko || description
        }
      };

      // 确保源语言保持原始名称和描述
      if (sourceLanguage === 'zh') {
        result.name.zh = name;
        result.description.zh = description;
      } else if (sourceLanguage === 'ja') {
        result.name.ja = name;
        result.description.ja = description;
      }

      console.log('Final dish translation result:', result);
      return result;
    } catch (parseError) {
      console.error('Error parsing dish translation response:', parseError);
      if (parseError instanceof Error) {
        console.error('Parse error details:', parseError.message);
      }
      console.log('Raw response content:', content);

      // 尝试清理内容并重新解析
      try {
        // 尝试提取 JSON 部分
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonContent = jsonMatch[0];
          console.log('Extracted JSON content:', jsonContent);

          const extractedTranslations = JSON.parse(jsonContent);
          console.log('Parsed extracted JSON:', extractedTranslations);

          const result = {
            name: {
              en: extractedTranslations.name?.en || name,
              zh: extractedTranslations.name?.zh || name,
              ja: extractedTranslations.name?.ja || name,
              ko: extractedTranslations.name?.ko || name
            },
            description: {
              en: extractedTranslations.description?.en || description,
              zh: extractedTranslations.description?.zh || description,
              ja: extractedTranslations.description?.ja || description,
              ko: extractedTranslations.description?.ko || description
            }
          };

          // 确保源语言保持原始名称和描述
          if (sourceLanguage === 'zh') {
            result.name.zh = name;
            result.description.zh = description;
          } else if (sourceLanguage === 'ja') {
            result.name.ja = name;
            result.description.ja = description;
          }

          console.log('Final dish translation result (after extraction):', result);
          return result;
        }
      } catch (extractError) {
        console.error('Failed to extract and parse JSON:', extractError);
      }

      // 返回默认值
      const defaultResult = {
        name: {
          en: name,
          zh: name,
          ja: name,
          ko: name
        },
        description: {
          en: description,
          zh: description,
          ja: description,
          ko: description
        }
      };

      console.log('Using default dish result:', defaultResult);
      return defaultResult;
    }
  } catch (error: any) {
    console.error('Translation error:', error);

    // 检查是否是配额错误
    if (error instanceof Error && error.message.includes('429')) {
      console.warn('OpenAI API quota exceeded. Using fallback translation.');
    } else if (error instanceof Error && error.message.includes('401')) {
      console.warn('OpenAI API authentication failed. Using fallback translation.');
    } else {
      console.warn('OpenAI API error. Using fallback translation.');
    }

    // 返回默认值
    return {
      name: {
        en: name,
        zh: name,
        ja: name,
        ko: name
      },
      description: {
        en: description,
        zh: description,
        ja: description,
        ko: description
      }
    };
  }
}
