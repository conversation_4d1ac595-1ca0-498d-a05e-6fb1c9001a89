/**
 * Admin internationalization utilities
 * Loads admin-specific translations from the separate admin directory
 */

import { notFound } from 'next/navigation';

type AdminMessages = {
  admin: {
    title: string;
    restaurants: string;
    users: string;
    analytics: {
      title: string;
      selectRestaurant: string;
      startDate: string;
      endDate: string;
      analysisType: string;
      summary: string;
      dishes: string;
      tables: string;
      time: string;
    };
    restaurant: {
      name: string;
      owner: string;
      status: string;
      createdAt: string;
      actions: string;
      view: string;
      edit: string;
      delete: string;
      active: string;
      inactive: string;
    };
    user: {
      email: string;
      role: string;
      restaurant: string;
      createdAt: string;
      lastLogin: string;
      actions: string;
    };
    common: {
      loading: string;
      error: string;
      success: string;
      save: string;
      cancel: string;
      confirm: string;
      search: string;
      filter: string;
      export: string;
      refresh: string;
    };
  };
};

/**
 * Load admin translations for a specific locale
 */
export async function getAdminMessages(locale: string): Promise<AdminMessages> {
  try {
    const messages = await import(`../i18n/admin/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load admin messages for locale: ${locale}`, error);
    notFound();
  }
}

/**
 * Get available admin locales
 */
export function getAdminLocales(): string[] {
  return ['en', 'zh', 'ja', 'ko'];
}

/**
 * Check if a locale is supported for admin
 */
export function isAdminLocaleSupported(locale: string): boolean {
  return getAdminLocales().includes(locale);
}
