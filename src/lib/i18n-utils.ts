/**
 * Internationalization utility functions
 * Provides helper functions for getting localized names and descriptions
 */

export type LocalizedEntity = {
  name_en?: string;
  name_zh?: string;
  name_ja?: string;
  name_ko?: string;
  description_en?: string;
  description_zh?: string;
  description_ja?: string;
  description_ko?: string;
};

/**
 * Get localized name based on current locale
 * Falls back to other languages if the current locale is not available
 * 
 * @param entity - Object with localized name fields
 * @param locale - Current locale (en, zh, ja, ko)
 * @param fallback - Default fallback text if no translation is found
 * @returns Localized name string
 */
export function getLocalizedName(
  entity: LocalizedEntity,
  locale: string,
  fallback: string = 'Unknown'
): string {
  // Try to get the name for the current locale
  const nameKey = `name_${locale}` as keyof LocalizedEntity;
  const currentLanguageName = entity[nameKey] as string;
  
  // If current language name exists, return it
  if (currentLanguageName && currentLanguageName.trim()) {
    return currentLanguageName;
  }
  
  // Fallback order: zh > en > ja > ko
  return (
    entity.name_en ||
    entity.name_ja ||
    entity.name_zh ||
    entity.name_ko ||
    fallback
  );
}

/**
 * Get localized description based on current locale
 * Falls back to other languages if the current locale is not available
 * 
 * @param entity - Object with localized description fields
 * @param locale - Current locale (en, zh, ja, ko)
 * @param fallback - Default fallback text if no translation is found
 * @returns Localized description string
 */
export function getLocalizedDescription(
  entity: LocalizedEntity,
  locale: string,
  fallback: string = ''
): string {
  // Try to get the description for the current locale
  const descKey = `description_${locale}` as keyof LocalizedEntity;
  const currentLanguageDesc = entity[descKey] as string;
  
  // If current language description exists, return it
  if (currentLanguageDesc && currentLanguageDesc.trim()) {
    return currentLanguageDesc;
  }
  
  // Fallback order: zh > en > ja > ko
  return (
    entity.description_en ||
    entity.description_ja ||
    entity.description_zh ||
    entity.description_ko ||
    fallback
  );
}

/**
 * Get dish name based on current locale
 * Specialized function for dish entities
 */
export function getDishName(
  dish: LocalizedEntity,
  locale: string
): string {
  return getLocalizedName(dish, locale, 'Unknown Dish');
}

/**
 * Get category name based on current locale
 * Specialized function for category entities
 */
export function getCategoryName(
  category: LocalizedEntity,
  locale: string
): string {
  return getLocalizedName(category, locale, 'Unknown Category');
}

/**
 * Get dish description based on current locale
 * Specialized function for dish entities
 */
export function getDishDescription(
  dish: LocalizedEntity,
  locale: string
): string {
  return getLocalizedDescription(dish, locale, '');
}

/**
 * Check if an entity has a name in the specified locale
 */
export function hasLocalizedName(
  entity: LocalizedEntity,
  locale: string
): boolean {
  const nameKey = `name_${locale}` as keyof LocalizedEntity;
  const name = entity[nameKey] as string;
  return !!(name && name.trim());
}

/**
 * Check if an entity has a description in the specified locale
 */
export function hasLocalizedDescription(
  entity: LocalizedEntity,
  locale: string
): boolean {
  const descKey = `description_${locale}` as keyof LocalizedEntity;
  const description = entity[descKey] as string;
  return !!(description && description.trim());
}

/**
 * Get all available locales for an entity's name
 */
export function getAvailableNameLocales(entity: LocalizedEntity): string[] {
  const locales: string[] = [];
  
  if (entity.name_en) locales.push('en');
  if (entity.name_zh) locales.push('zh');
  if (entity.name_ja) locales.push('ja');
  if (entity.name_ko) locales.push('ko');
  
  return locales;
}

/**
 * Get all available locales for an entity's description
 */
export function getAvailableDescriptionLocales(entity: LocalizedEntity): string[] {
  const locales: string[] = [];
  
  if (entity.description_en) locales.push('en');
  if (entity.description_zh) locales.push('zh');
  if (entity.description_ja) locales.push('ja');
  if (entity.description_ko) locales.push('ko');
  
  return locales;
}
