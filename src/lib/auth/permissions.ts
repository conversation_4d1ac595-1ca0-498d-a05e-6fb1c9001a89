import { createClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export interface UserPermissions {
  user: any;
  userData: any;
  roleName: string;
  restaurantId: string | null;
  isAdmin: boolean;
  isKitchen: boolean;
  isRestaurantOwner: boolean;
}

/**
 * 验证用户权限的统一函数
 */
export async function validateUserPermissions(request: NextRequest): Promise<UserPermissions | null> {
  try {
    const supabase = await createClient();
    
    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('权限验证 - 获取用户错误:', userError);
      return null;
    }

    // 获取用户角色和餐厅信息
    const { data: userData, error } = await supabase
      .from('users')
      .select('role_id, restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (error || !userData) {
      console.log('权限验证 - 获取用户数据错误:', error);
      return null;
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;
    const restaurantId = userData.restaurant_id;

    return {
      user,
      userData,
      roleName,
      restaurantId,
      isAdmin: roleName === 'super_admin' || roleName === 'admin',
      isKitchen: roleName === 'kitchen',
      isRestaurantOwner: roleName === 'restaurant_owner'
    };
  } catch (error) {
    console.error('权限验证错误:', error);
    return null;
  }
}

/**
 * 检查用户是否有访问厨房的权限
 */
export function hasKitchenAccess(permissions: UserPermissions): boolean {
  return permissions.isAdmin || permissions.isKitchen || permissions.isRestaurantOwner;
}

/**
 * 检查用户是否有管理员权限
 */
export function hasAdminAccess(permissions: UserPermissions): boolean {
  return permissions.isAdmin;
}

/**
 * 检查用户是否可以访问指定餐厅的数据
 */
export function canAccessRestaurant(permissions: UserPermissions, targetRestaurantId: string): boolean {
  // 管理员可以访问所有餐厅
  if (permissions.isAdmin) {
    return true;
  }
  
  // 其他用户只能访问自己的餐厅
  return permissions.restaurantId === targetRestaurantId;
}

/**
 * 创建权限验证中间件
 */
export function createPermissionMiddleware(
  requiredPermission: 'kitchen' | 'admin' | 'restaurant_owner',
  requireRestaurantAccess: boolean = true
) {
  return async function(request: NextRequest, targetRestaurantId?: string) {
    const permissions = await validateUserPermissions(request);
    
    if (!permissions) {
      return NextResponse.json(
        { error: 'Unauthorized. Please log in.' },
        { status: 401 }
      );
    }

    // 检查基本权限
    let hasPermission = false;
    switch (requiredPermission) {
      case 'admin':
        hasPermission = hasAdminAccess(permissions);
        break;
      case 'kitchen':
        hasPermission = hasKitchenAccess(permissions);
        break;
      case 'restaurant_owner':
        hasPermission = permissions.isRestaurantOwner || permissions.isAdmin;
        break;
    }

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden. Insufficient permissions.' },
        { status: 403 }
      );
    }

    // 检查餐厅访问权限
    if (requireRestaurantAccess && targetRestaurantId) {
      if (!canAccessRestaurant(permissions, targetRestaurantId)) {
        return NextResponse.json(
          { error: 'Forbidden. Cannot access this restaurant data.' },
          { status: 403 }
        );
      }
    }

    return permissions;
  };
}

/**
 * 简化的权限检查函数，用于API路由
 */
export async function requireKitchenAccess(request: NextRequest, targetRestaurantId?: string): Promise<UserPermissions | NextResponse> {
  const middleware = createPermissionMiddleware('kitchen', !!targetRestaurantId);
  return await middleware(request, targetRestaurantId);
}

export async function requireAdminAccess(request: NextRequest): Promise<UserPermissions | NextResponse> {
  const middleware = createPermissionMiddleware('admin', false);
  return await middleware(request);
}

export async function requireRestaurantOwnerAccess(request: NextRequest, targetRestaurantId?: string): Promise<UserPermissions | NextResponse> {
  const middleware = createPermissionMiddleware('restaurant_owner', !!targetRestaurantId);
  return await middleware(request, targetRestaurantId);
}
