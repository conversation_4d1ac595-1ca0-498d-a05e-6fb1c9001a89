import { getRequestConfig } from 'next-intl/server';

export const locales = ['en', 'ja', 'ko', 'zh'] as const;
export type Locale = (typeof locales)[number];

/**
 * Load messages from both regular messages and admin directories
 */
async function loadMessages(locale: string) {
  try {
    // Load regular messages
    const regularMessages = (await import(`./i18n/messages/${locale}.json`)).default;

    // Load admin messages
    const adminMessages = (await import(`./i18n/admin/${locale}.json`)).default;

    // Merge the messages
    return {
      ...regularMessages,
      ...adminMessages
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);

    // Fallback to English if the locale fails to load
    if (locale !== 'en') {
      try {
        const regularMessages = (await import(`./i18n/messages/en.json`)).default;
        const adminMessages = (await import(`./i18n/admin/en.json`)).default;
        return {
          ...regularMessages,
          ...adminMessages
        };
      } catch (fallbackError) {
        console.error('Failed to load fallback English messages', fallbackError);
        return {};
      }
    }

    return {};
  }
}

export default getRequestConfig(async ({ locale }) => {
  const currentLocale = locale || 'en';
  return {
    locale: currentLocale,
    messages: await loadMessages(currentLocale)
  };
});

/**
 * Utility function to get available locales
 */
export function getAvailableLocales(): readonly string[] {
  return locales;
}

/**
 * Check if a locale is supported
 */
export function isLocaleSupported(locale: string): boolean {
  return locales.includes(locale as Locale);
}
