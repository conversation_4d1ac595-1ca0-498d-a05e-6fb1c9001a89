import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import createIntlMiddleware from 'next-intl/middleware';

// 国际化中间件
const intlMiddleware = createIntlMiddleware({
  locales: ['en', 'ja', 'ko', 'zh'],
  defaultLocale: 'en',
  localePrefix: 'always',
});

export async function middleware(request: NextRequest) {
  // 首先应用国际化中间件
  const intlResponse = intlMiddleware(request);

  // 获取请求中的 cookie
  // 查找所有以 sb- 开头的 cookie，因为 Supabase 使用 sb-<project-ref>-auth-token 格式
  const authCookies = Array.from(request.cookies.getAll())
    .filter(cookie => cookie.name.startsWith('sb-'));

  // 打印所有 Supabase 相关的 cookie
  console.log('所有 Supabase Cookie:', authCookies.map(c => `${c.name}=${c.value.substring(0, 10)}...`).join(', '));

  // 获取 auth token cookie
  // 首先尝试获取项目特定的 cookie (sb-<project-ref>-auth-token)
  let authCookie = authCookies.find(cookie => cookie.name.endsWith('-auth-token'));

  // 如果没有找到，尝试获取通用的 cookie (sb-auth-token)
  if (!authCookie) {
    authCookie = authCookies.find(cookie => cookie.name === 'sb-auth-token');
  }

  const supabaseCookie = authCookie?.value;

  // 添加调试信息
  console.log('Auth Cookie:', authCookie?.name, supabaseCookie ? '存在' : '不存在');

  const url = request.nextUrl.clone();
  const { pathname } = url;

  // 添加调试信息
  console.log('中间件 - 请求路径:', pathname);
  console.log('中间件 - 认证令牌:', supabaseCookie ? '存在' : '不存在');

  // 创建 Supabase 客户端
  // 使用非公开环境变量，如果不存在则回退到公开变量
  const supabaseUrl = process.env.NEXT_SUPABASE_URL || process.env.SUPABASE_URL!;
  const supabaseAnonKey = process.env.NEXT_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY!;

  const supabase = createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(name) {
          return request.cookies.get(name)?.value;
        },
        set(name, value, options) {
          // 中间件中不需要设置 cookie
          return;
        },
        remove(name) {
          // 中间件中不需要删除 cookie
          return;
        },
      },
    }
  );

  // 打印更多调试信息
  console.log('中间件 - Supabase URL:', supabaseUrl);

  // 检查会话和用户信息
  // 首先获取会话
  const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
  const session = sessionData.session;

  // 如果有会话，使用 getUser 获取经过验证的用户信息
  let user = null;
  if (session) {
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (!userError) {
      user = userData.user;
    } else {
      console.log('中间件 - 获取用户信息错误:', userError);
    }
  }

  // 添加调试信息
  console.log('中间件 - 会话状态:', session ? '已登录' : '未登录');
  console.log( new Date((session?.expires_at ?? 0) * 1000).toLocaleString());
  if (sessionError) {
    console.log('中间件 - 会话获取错误:', sessionError);
  }
  if (user) {
    console.log('中间件 - 用户ID:', user.id);
  }

  // 提取语言代码
  const locale = pathname.split('/')[1];

  // 检查是否是受保护的路由
  const isAdminRoute = pathname.includes(`/${locale}/admin`);
  const isKitchenRoute = pathname.includes(`/${locale}/kitchen`);

  // 检查是否是公开路由
  const isLandingRoute = pathname.includes(`/${locale}/landing`);
  const isPricingRoute = pathname.includes(`/${locale}/pricing`);
  const isRegisterRoute = pathname.includes(`/${locale}/register`);
  const isCompleteRegistrationRoute = pathname.includes(`/${locale}/complete-registration`);
  const isAuthVerifyRoute = pathname.includes('/auth/verify');
  const isPublicRoute = isLandingRoute || isPricingRoute || isRegisterRoute || isCompleteRegistrationRoute || isAuthVerifyRoute;

  // 添加更多调试信息
  console.log('中间件 - 路由类型:',
    isAdminRoute ? '管理员路由' :
    isKitchenRoute ? '厨房路由' :
    isPublicRoute ? '公开路由' : '普通路由'
  );
  console.log('中间件 - 用户状态:', user ? '已登录' : '未登录');

  // 如果是受保护的路由，但没有用户，重定向到登录页面
  if ((isAdminRoute || isKitchenRoute) && !user) {
    console.log('中间件 - 未登录访问受保护路由，重定向到登录页面');
    url.pathname = `/${locale}/login`;
    return NextResponse.redirect(url);
  }

  // 如果有用户，检查用户角色
  if (user && (isAdminRoute || isKitchenRoute)) {
    // 获取用户角色
    const { data: userData, error } = await supabase
      .from('users')
      .select('role_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (error || !userData) {
      // 如果出错或找不到用户，重定向到登录页面
      console.log('中间件 - 用户数据获取失败:', error);
      url.pathname = `/${locale}/login`;
      return NextResponse.redirect(url);
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;

    console.log('中间件 - 用户角色:', roleName);

    // 检查权限
    if (isAdminRoute && roleName !== 'super_admin' && roleName !== 'admin') {
      // 如果尝试访问管理员页面，但不是管理员，重定向到厨房页面或首页
      url.pathname = roleName === 'kitchen' ? `/${locale}/kitchen` : `/${locale}`;
      return NextResponse.redirect(url);
    }

    if (isKitchenRoute && roleName !== 'super_admin' && roleName !== 'admin' && roleName !== 'kitchen') {
      // 如果尝试访问厨房页面，但没有权限，重定向到首页
      url.pathname = `/${locale}`;
      return NextResponse.redirect(url);
    }
  }

  // 对于其他路由，应用国际化中间件
  return intlResponse;
}

export const config = {
  // 匹配所有路径，除了 API 路由、静态文件和认证路由
  matcher: ['/((?!api|_next|.*\\..*).*)']
};
