'use client';

import Head from 'next/head';
import { useTranslations } from 'next-intl';

interface MetaTagsProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
  locale?: string;
}

export default function MetaTags({
  title,
  description,
  keywords,
  ogImage = '/images/qr-code-ordering.png',
  ogUrl,
  locale = 'en',
}: MetaTagsProps) {
  const t = useTranslations('landing');
  
  const pageTitle = title || t('title');
  const pageDescription = description || t('subtitle');
  const pageKeywords = keywords || 'QR code ordering, restaurant ordering system, digital menu, contactless ordering';
  const pageUrl = ogUrl || 'https://kyotokitchenqr.com';

  return (
    <Head>
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={pageKeywords} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={pageUrl} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:locale" content={locale} />
      
      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={pageUrl} />
      <meta property="twitter:title" content={pageTitle} />
      <meta property="twitter:description" content={pageDescription} />
      <meta property="twitter:image" content={ogImage} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={pageUrl} />
    </Head>
  );
}
