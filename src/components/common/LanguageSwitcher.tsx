'use client';

import { useTranslations,useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useState } from 'react';

const locales = ['en', 'ja', 'ko', 'zh'];

export default function LanguageSwitcher() {
  const t = useTranslations('common');
  const pathname = usePathname();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  // const [currentLng, setCurrentLng] = useState(localStorage.getItem('preferred_language') || 'en');
  const currentLng = useLocale();
  const languageNames: Record<string, string> = {
    en: t('english'),
    ja: t('japanese'),
    ko: t('korean'),
    zh: t('chinese'),
  };

  const handleLanguageChange = (locale: string) => {
    // Get the current path without the locale prefix
    const currentPath = pathname || '';
    const segments = currentPath.split('/');
    segments[1] = locale; // Replace the locale segment

    // Save the user's language preference to localStorage
    localStorage.setItem('preferred_language', locale);

    // Navigate to the new path
    router.push(segments.join('/'));
    setIsOpen(false);
  };
  //{t('language')}
  // const current_lng = localStorage.getItem('preferred_language') || 'en';
  return (
    <div className="relative">
      <button
        type="button"
        className="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        onClick={() => setIsOpen(!isOpen)}
      >
        {languageNames[currentLng]}
        <svg
          className="-mr-1 ml-2 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20">
          <div
            className="py-1"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="options-menu"
          >
            {locales.map((locale) => (
              <button
                key={locale}
                onClick={() => handleLanguageChange(locale)}
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                role="menuitem"
              >
                {languageNames[locale]}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
