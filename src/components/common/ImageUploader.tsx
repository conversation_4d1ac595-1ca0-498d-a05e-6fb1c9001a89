'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

interface ImageUploaderProps {
  onImageUploaded: (imageUrl: string) => void;
  currentImageUrl?: string;
  onUploadStatusChange?: (isUploading: boolean) => void;
  translations: {
    upload: string;
    dragDrop: string;
    or: string;
    browse: string;
    uploading: string;
    maxSize: string;
    invalidType: string;
    uploadFailed: string;
  };
}

export default function ImageUploader({
  onImageUploaded,
  currentImageUrl,
  onUploadStatusChange,
  translations
}: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 当 currentImageUrl 变化时更新预览
  useEffect(() => {
    if (currentImageUrl) {
      setPreviewUrl(currentImageUrl);
    }
  }, [currentImageUrl]);

  // 处理文件选择
  const handleFileChange = async (file: File) => {
    // 重置错误
    setError(null);

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setError(translations.invalidType);
      return;
    }

    // 验证文件大小（5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      setError(translations.maxSize);
      return;
    }

    // 开始上传
    setIsUploading(true);
    onUploadStatusChange?.(true);

    try {
      // 创建本地预览
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // 获取预签名 URL
      const presignedResponse = await fetch('/api/upload/presigned-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileType: file.type,
          fileSize: file.size,
          fileName: file.name,
        }),
      });

      if (!presignedResponse.ok) {
        throw new Error('Failed to get presigned URL');
      }

      const { presignedUrl, publicUrl } = await presignedResponse.json();

      // 上传文件到 R2
      const uploadResponse = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'PUT',
        },
        body: file,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }

      // 上传成功，通知父组件
      onImageUploaded(publicUrl);
    } catch (error: any) {
      console.error('Upload error:', error);
      setError(translations.uploadFailed);
      // 如果上传失败，清除预览
      if (!currentImageUrl) {
        setPreviewUrl(null);
      } else {
        setPreviewUrl(currentImageUrl);
      }
    } finally {
      setIsUploading(false);
      onUploadStatusChange?.(false);
    }
  };

  // 处理文件输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileChange(file);
      // 重置文件输入框，这样即使选择相同的文件也会触发 onChange 事件
      e.target.value = '';
    }
  };

  // 处理拖放
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileChange(file);
    }
  };

  // 处理点击上传区域
  const handleAreaClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full">
      <div
        className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleAreaClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleInputChange}
          accept="image/jpeg,image/png,image/gif,image/webp"
          className="hidden"
        />

        {previewUrl ? (
          <div className="relative h-48 w-full mb-2">
            <Image
              src={previewUrl}
              alt="Preview"
              fill
              style={{ objectFit: 'contain' }}
              onError={() => {
                // 图片加载失败时显示占位符
                setPreviewUrl('https://via.placeholder.com/150?text=No+Image');
              }}
            />
          </div>
        ) : (
          <div className="py-4">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        )}

        <div className="text-sm text-gray-600">
          {isUploading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {translations.uploading}
            </div>
          ) : (
            <>
              <p>{translations.dragDrop}</p>
              <p className="mt-1">{translations.or}</p>
              <p className="mt-1 text-blue-500">{translations.browse}</p>
            </>
          )}
        </div>
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
