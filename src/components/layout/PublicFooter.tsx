'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useLocale } from 'next-intl';

interface PublicFooterProps {
  variant?: 'full' | 'simple';
}

export default function PublicFooter({ variant = 'full' }: PublicFooterProps) {
  const t = useTranslations('landing');
  const locale = useLocale();

  const features = [
    {
      title: t('featureMultilingual'),
      description: t('featureMultilingualDesc'),
    },
    {
      title: t('featureQRCode'),
      description: t('featureQRCodeDesc'),
    },
    {
      title: t('featureRealTime'),
      description: t('featureRealTimeDesc'),
    },
    {
      title: t('featureKitchen'),
      description: t('featureKitchenDesc'),
    },
  ];

  const steps = [
    {
      title: t('step1'),
      description: t('step1Desc'),
    },
    {
      title: t('step2'),
      description: t('step2Desc'),
    },
    {
      title: t('step3'),
      description: t('step3Desc'),
    },
    {
      title: t('step4'),
      description: t('step4Desc'),
    },
  ];

  if (variant === 'simple') {
    return (
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-4">
            <h3 className="text-xl font-bold">{t('title')}</h3>
          </div>
          <p className="mb-4">{t('subtitle')}</p>
          <p className="mb-2">
            {t('contactEmail')}
            <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
              <EMAIL>
            </a>
          </p>
          <p>&copy; 2025 oisii.life All rights reserved.</p>
        </div>
      </footer>
    );
  }

  return (
    <footer className="bg-gray-800 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">{t('title')}</h3>
            <p className="mb-4">{t('subtitle')}</p>
            <p className="mb-4">
              {t('contactEmail')}
              <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300">
                <EMAIL>
              </a>
            </p>
            <div className="space-y-2">
              <Link href={`/${locale}/pricing`} className="block text-gray-300 hover:text-white">
                {t('viewPricing')}
              </Link>
              <Link href={`/${locale}/register`} className="block text-gray-300 hover:text-white">
                {t('registerNow')}
              </Link>
            </div>
          </div>
          <div>
            <h3 className="text-xl font-bold mb-4">{t('features')}</h3>
            <ul className="space-y-2">
              {features.slice(0, 4).map((feature, index) => (
                <li key={index} className="text-gray-300">{feature.title}</li>
              ))}
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-bold mb-4">{t('howItWorks')}</h3>
            <ul className="space-y-2">
              {steps.map((step, index) => (
                <li key={index} className="text-gray-300">{step.title}</li>
              ))}
            </ul>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t border-gray-700 text-center">
          <p>&copy; 2025 oisii.life All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
