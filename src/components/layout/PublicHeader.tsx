'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';

export default function PublicHeader() {
  const t = useTranslations('landing');
  const locale = useLocale();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        // 检查localStorage中是否存在登录相关的key
        const restaurantId = localStorage.getItem('restaurant_id');
        const authToken = localStorage.getItem('auth_token');

        // 如果存在餐厅ID或认证token，认为用户已登录
        setIsLoggedIn(!!(restaurantId || authToken));
      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsLoggedIn(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'restaurant_id' || e.key === 'auth_token') {
        checkAuthStatus();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 也可以监听自定义事件，用于同一页面内的状态变化
    const handleAuthChange = () => {
      checkAuthStatus();
    };

    window.addEventListener('authStateChange', handleAuthChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('authStateChange', handleAuthChange);
    };
  }, []);

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="oisii.life"
            width={60}
            height={60}
          />
          <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600 ml-2">
            OISII
          </Link>
        </div>
        <div className="flex items-center space-x-4">
          <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
            {t('home')}
          </Link>
          <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
            {t('viewPricing')}
          </Link>

          {!loading && (
            <>
              {isLoggedIn ? (
                <Link
                  href={`/${locale}/kitchen`}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                >
                  My Kitchen
                </Link>
              ) : (
                <>
                  <Link href={`/${locale}/login`} className="text-gray-600 hover:text-gray-900">
                    {t('login')}
                  </Link>
                  <Link href={`/${locale}/register`} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                    {t('registerNow')}
                  </Link>
                </>
              )}
            </>
          )}

          <LanguageSwitcher />
        </div>
      </div>
    </header>
  );
}
