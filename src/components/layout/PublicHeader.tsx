'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { useLocale } from 'next-intl';
import { useEffect, useState } from 'react';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import { createClient } from '@/lib/supabase/client';

export default function PublicHeader() {
  const t = useTranslations('landing');
  const locale = useLocale();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        setIsLoggedIn(!!session);
      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsLoggedIn(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();

    // Listen for auth changes
    const supabase = createClient();
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setIsLoggedIn(!!session);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <Image
            src="/logo.png"
            alt="oisii.life"
            width={60}
            height={60}
          />
          <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600 ml-2">
            OISII
          </Link>
        </div>
        <div className="flex items-center space-x-4">
          <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
            {t('home')}
          </Link>
          <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
            {t('viewPricing')}
          </Link>

          {!loading && (
            <>
              {isLoggedIn ? (
                <Link
                  href={`/${locale}/kitchen`}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                >
                  My Kitchen
                </Link>
              ) : (
                <>
                  <Link href={`/${locale}/login`} className="text-gray-600 hover:text-gray-900">
                    {t('login')}
                  </Link>
                  <Link href={`/${locale}/register`} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
                    {t('registerNow')}
                  </Link>
                </>
              )}
            </>
          )}

          <LanguageSwitcher />
        </div>
      </div>
    </header>
  );
}
