'use client';

import Head from 'next/head';
import PublicHeader from './PublicHeader';
import PublicFooter from './PublicFooter';

interface PublicLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  footerVariant?: 'full' | 'simple';
}

export default function PublicLayout({ 
  children, 
  title = 'OISII.LIFE - Modern QR Code Ordering System for Japanese Restaurants',
  description = 'Streamline your restaurant operations with our QR code ordering system. Support for multiple languages, real-time updates, and kitchen management.',
  keywords = 'QR code ordering, restaurant ordering system, digital menu, contactless ordering, Japanese restaurant',
  footerVariant = 'full'
}: PublicLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <Head>
        <link rel="icon" href="/favicon.ico" />
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
      </Head>
      
      <PublicHeader />
      
      <main>
        {children}
      </main>
      
      <PublicFooter variant={footerVariant} />
    </div>
  );
}
