'use client';

import { useState, useEffect, useRef } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import Image from 'next/image';

import { useCartStore } from '@/lib/stores/cartStore';

type Category = {
  id: string;
  name: string;
};

type Dish = {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category_id: string;
  is_discounted: boolean;
  discount_percentage: number;
};

export default function CustomerMenu({ tableId }: { tableId: string }) {
  const t = useTranslations();
  const locale = useLocale() as string;
  const [categories, setCategories] = useState<Category[]>([]);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);
  const { items, addToCart, updateQuantity, removeFromCart } = useCartStore();

  const categoryRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const dishesContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Verify token before fetching data
    const verifyToken = async () => {
      const token = localStorage.getItem('auth_token');
      const storedRestaurantId = localStorage.getItem('restaurant_id');
      const storedTableId = localStorage.getItem('table_id');
      const tokenExpiry = localStorage.getItem('token_expiry');

      // Check if token exists and hasn't expired
      if (!token || !storedRestaurantId || !storedTableId || !tokenExpiry) {
        setAuthError('Authentication required. Please scan the QR code again.');
        showRescanPrompt();
        return false;
      }

      // Check if token has expired
      const expiryTime = new Date(tokenExpiry);
      const currentTime = new Date();

      if (currentTime > expiryTime) {
        setAuthError('Your session has expired. Please scan the QR code again.');
        showRescanPrompt();
        return false;
      }

      // Verify that the table ID matches the current route
      if (storedTableId !== tableId) {
        setAuthError('Table mismatch. Please scan the correct QR code.');
        showRescanPrompt();
        return false;
      }

      // Verify token with the server
      try {
        const response = await fetch('/api/auth/verify-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            token,
            restaurantId: storedRestaurantId,
            tableId: storedTableId,
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.valid) {
          setAuthError('Invalid authentication. Please scan the QR code again.');
          showRescanPrompt();
          return false;
        }

        return true;
      } catch (error: any) {
        // console.error('Error verifying token:', error);
        setAuthError('Authentication error. Please try again.');
        showRescanPrompt();
        return false;
      }
    };

    // Show rescan prompt instead of redirecting
    const showRescanPrompt = () => {
      // Clear stored data
      localStorage.removeItem('auth_token');
      localStorage.removeItem('restaurant_id');
      localStorage.removeItem('table_id');
      localStorage.removeItem('token_expiry');
      localStorage.removeItem('order_id');
      localStorage.removeItem('guest_count');

      // Show user-friendly message instead of redirecting
      setAuthError('Please scan the QR code again to continue ordering.');
    };

    async function fetchData() {
      setLoading(true);

      // Verify token before fetching data
      const isAuthenticated = await verifyToken();
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }

      // 获取存储的餐厅 ID
      const restaurantId = localStorage.getItem('restaurant_id');
      if (!restaurantId) {
        console.error('Restaurant ID not found in localStorage');
        setLoading(false);
        return;
      }

      try {
        // 使用 API 路由获取类别数据
        const categoriesResponse = await fetch(`/api/categories?restaurantId=${restaurantId}`);
        if (!categoriesResponse.ok) {
          throw new Error(`Error fetching categories: ${categoriesResponse.statusText}`);
        }

        const categoriesData = await categoriesResponse.json();

        // 确保我们使用 data 属性中的数据
        const categories = categoriesData.data || [];

        const formattedCategories = categories.map((category: any) => ({
          id: category.id,
          name: category[`name_${locale}`] || category.name_en
        }));

        setCategories(formattedCategories);

        if (formattedCategories.length > 0) {
          setActiveCategory(formattedCategories[0].id);
        }

        // 使用 API 路由获取菜品数据
        const dishesResponse = await fetch(`/api/dishes?restaurantId=${restaurantId}`);
        if (!dishesResponse.ok) {
          throw new Error(`Error fetching dishes: ${dishesResponse.statusText}`);
        }

        const dishesData = await dishesResponse.json();

        // 确保我们使用 data 属性中的数据
        const dishes = dishesData.data || [];

        const formattedDishes = dishes.map((dish: any) => ({
          id: dish.id,
          name: dish[`name_${locale}`] || dish.name_en,
          description: dish[`description_${locale}`] || dish.description_en || '',
          price: dish.price,
          image_url: dish.image_url || '/placeholder-dish.jpg',
          category_id: dish.category_id,
          is_discounted: dish.is_discounted,
          discount_percentage: dish.discount_percentage
        }));

        setDishes(formattedDishes);
      } catch (error: any) {
        console.error('Error fetching menu data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [locale]);

  useEffect(() => {
    const handleScroll = () => {
      if (!dishesContainerRef.current) return;

      const containerTop = dishesContainerRef.current.getBoundingClientRect().top;
      let closestCategory = null;
      let closestDistance = Infinity;

      Object.entries(categoryRefs.current).forEach(([categoryId, ref]) => {
        if (!ref) return;

        const refTop = ref.getBoundingClientRect().top;
        const distance = Math.abs(refTop - containerTop);

        if (distance < closestDistance) {
          closestDistance = distance;
          closestCategory = categoryId;
        }
      });

      if (closestCategory && closestCategory !== activeCategory) {
        setActiveCategory(closestCategory);
      }
    };

    const container = dishesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [activeCategory]);

  const scrollToCategory = (categoryId: string) => {
    const ref = categoryRefs.current[categoryId];
    if (ref && dishesContainerRef.current) {
      dishesContainerRef.current.scrollTo({
        top: ref.offsetTop - 20,
        behavior: 'smooth'
      });
    }
  };

  // 获取菜品在购物车中的未提交数量
  const getDishQuantityInCart = (dishId: string): number => {
    const cartItem = items.find(item =>
      item.id === dishId &&
      item.tableId === tableId &&
      !item.submitted
    );
    return cartItem ? cartItem.quantity : 0;
  };

  // 添加到购物车
  const handleAddToCart = (dish: Dish) => {
    addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      quantity: 1,
      tableId
    });
  };

  // 更新购物车中的数量
  const handleUpdateQuantity = (dishId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(dishId, tableId);
    } else {
      updateQuantity(dishId, quantity, tableId);
    }
  };

  if (authError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-md w-full text-center">
          <div className="text-orange-500 mb-4">
            <svg className="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3">{t('customer.sessionExpired')}</h3>
          <p className="text-gray-600 mb-6 leading-relaxed">{authError}</p>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-center mb-2">
                <svg className="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M20 12h.01M12 8h4.01M16 8h.01M8 12h.01M12 16h.01M16 12h.01M12 20h.01M16 20h.01M20 16h.01M20 8h.01M8 8h.01M8 16h.01M8 20h.01M4 12h.01M4 8h.01M4 16h.01M4 20h.01" />
                </svg>
              </div>
              <p className="text-sm text-blue-700 font-medium">{t('customer.rescanInstructions')}</p>
            </div>

            {/* <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors font-medium"
            >
              {t('customer.tryAgain')}
            </button> */}
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const getDiscountedPrice = (price: number, isDiscounted: boolean, discountPercentage: number) => {
    if (!isDiscounted || discountPercentage <= 0) return price;
    return price * (1 - discountPercentage / 100);
  };

  return (
    <div className="flex h-[calc(100vh-200px)]">
      {/* Categories sidebar */}
      <div className="w-1/4 pr-4 overflow-y-auto">
        <div className="sticky top-4">
          <h2 className="text-lg font-semibold mb-4 text-gray-600">{t('common.categories')}</h2>
          <div className="space-y-2">
            {categories.map(category => (
              <button
                key={category.id}
                className={`block w-full text-left px-3 py-2 rounded-md ${
                  activeCategory === category.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
                }`}
                onClick={() => {
                  setActiveCategory(category.id);
                  scrollToCategory(category.id);
                }}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Dishes list */}
      <div
        ref={dishesContainerRef}
        className="w-3/4 overflow-y-auto pr-4"
      >
        {categories.map(category => {
          const categoryDishes = dishes.filter(dish =>
            dish.category_id === category.id
          );

          if (categoryDishes.length === 0) return null;

          return (
            <div
              key={category.id}
              ref={(el) => { categoryRefs.current[category.id] = el; }}
              className="mb-8"
            >
              <h2 className="text-gray-600 text-xl font-semibold mb-4 sticky top-0 bg-white py-2 z-10">
                {category.name}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {categoryDishes.map(dish => (
                  <div key={dish.id} className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex">
                      <div className="w-1/3 relative h-32">
                        <Image
                          src={dish.image_url}
                          alt={dish.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div className="w-2/3 p-4 flex flex-col justify-between">
                        <h3 className="font-medium text-gray-800">{dish.name}</h3>
                        <p className="text-sm text-gray-700 line-clamp-2 mb-2">{dish.description}</p>
                        {/* 价格信息 */}
                        <div className="mb-2">
                          {dish.is_discounted ? (
                            <div>
                              <span className="text-gray-600 line-through text-sm mr-2">
                                ${dish.price.toFixed(2)}
                              </span>
                              <span className="text-red-600 font-medium">
                                ${getDiscountedPrice(dish.price, dish.is_discounted, dish.discount_percentage).toFixed(2)}
                              </span>
                            </div>
                          ) : (
                            <span className="font-medium text-gray-600">${dish.price.toFixed(2)}</span>
                          )}
                        </div>

                        {/* 添加到购物车按钮或数量控制器 */}
                        <div className="mt-auto">
                          {getDishQuantityInCart(dish.id) > 0 ? (
                            <div className="flex items-center justify-between bg-gray-100 rounded-md p-1">
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleUpdateQuantity(dish.id, getDishQuantityInCart(dish.id) - 1);
                                }}
                                className="w-8 h-8 flex items-center justify-center text-gray-700 hover:bg-gray-200 rounded-md"
                              >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                </svg>
                              </button>
                              <span className="font-medium text-gray-800">{getDishQuantityInCart(dish.id)}</span>
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleUpdateQuantity(dish.id, getDishQuantityInCart(dish.id) + 1);
                                }}
                                className="w-8 h-8 flex items-center justify-center text-gray-700 hover:bg-gray-200 rounded-md"
                              >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleAddToCart(dish);
                              }}
                              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-md text-sm w-full text-center transition-colors duration-200"
                            >
                              {t('customer.addToCart')}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
