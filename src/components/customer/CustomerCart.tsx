'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

import { useCartStore, CartItem } from '@/lib/stores/cartStore';
import { apiPost } from '@/lib/api';

export default function CustomerCart({ tableId }: { tableId: string }) {
  const t = useTranslations();
  const { removeFromCart, updateQuantity, markAsSubmitted, getUnsubmittedItems, getSubmittedItems } = useCartStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取未提交和已提交的商品
  const unsubmittedItems = getUnsubmittedItems(tableId);
  const submittedItems = getSubmittedItems(tableId);

  const calculateTotal = () => {
    return unsubmittedItems.reduce((total, item) => total + item.price * item.quantity, 0);
  };

  const handleSubmitOrder = async () => {
    if (unsubmittedItems.length === 0) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // Verify token before submitting order
      const token = localStorage.getItem('auth_token');
      const storedRestaurantId = localStorage.getItem('restaurant_id');
      const storedTableId = localStorage.getItem('table_id');
      const tokenExpiry = localStorage.getItem('token_expiry');

      console.log('🔐 Token validation:', {
        hasToken: !!token,
        tokenLength: token?.length,
        storedRestaurantId,
        storedTableId,
        currentTableId: tableId,
        tokenExpiry,
        isExpired: tokenExpiry ? new Date() > new Date(tokenExpiry) : 'no expiry'
      });

      if (!token || !storedRestaurantId || !storedTableId || storedTableId !== tableId) {
        setError('Authentication required. Please scan the QR code again.');
        setIsSubmitting(false);
        return;
      }

      // Verify token with the server
      const verifyResponse = await fetch('/api/auth/verify-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          restaurantId: storedRestaurantId,
          tableId: storedTableId,
        }),
      });

      const verifyData = await verifyResponse.json();

      if (!verifyResponse.ok || !verifyData.valid) {
        setError('Your session has expired. Please scan the QR code again.');
        setIsSubmitting(false);
        return;
      }

      // 使用预先创建的订单ID
      const storedOrderId = localStorage.getItem('order_id');

      if (!storedOrderId) {
        setError('Order ID not found. Please scan the QR code again.');
        setIsSubmitting(false);
        return;
      }

      // 验证订单是否存在且未完成
      const orderResponse = await fetch(`/api/orders/${storedOrderId}?tableId=${tableId}`);
      const orderData = await orderResponse.json();

      if (!orderResponse.ok || !orderData.data) {
        setError('Order not found or already completed. Please scan the QR code again.');
        setIsSubmitting(false);
        return;
      }

      // Add order items
      const orderItems = unsubmittedItems.map(item => ({
        order_id: storedOrderId,
        dish_id: item.id,
        quantity: item.quantity,
        price: item.price,
        status: 'pending'
      }));

      // 获取客人数量
      const guestCount = localStorage.getItem('guest_count');

      // 提交订单项
      const response = await apiPost('/order-items', {
        orderItems,
        token: token, // 添加token进行验证
        guestCount: guestCount ? parseInt(guestCount) : 1 // 传递客人数量
      });

      if (!response.success) {
        throw new Error('Failed to submit order items');
      }

      // 如果创建了新订单，更新localStorage中的订单ID
      if (response.newOrderCreated && response.orderId) {
        console.log('📋 New order created:', response.orderId);
        localStorage.setItem('order_id', response.orderId);
      }

      // 标记购物车中的商品为已提交
      markAsSubmitted(tableId, response.orderId || storedOrderId);
      setOrderPlaced(true);

      // Reset order placed status after 5 seconds
      setTimeout(() => {
        setOrderPlaced(false);
      }, 5000);

    } catch (error: any) {
      console.error('Error submitting order:', error);
      setError('An error occurred while submitting your order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (orderPlaced) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-8">
          <svg
            className="mx-auto h-12 w-12 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
          <h2 className="mt-2 text-lg font-medium text-gray-900">
            {t('customer.orderPlaced')}
          </h2>
          <p className="mt-1 text-sm text-gray-600">
            {t('customer.continueShopping')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
      <h2 className="text-gray-900 text-lg font-semibold mb-4">{t('common.cart')}</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* 当前购物车部分 */}
      <div className="mb-6">
        <h3 className="font-medium text-gray-600 mb-3 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          {t('customer.currentCart')}
        </h3>

        {unsubmittedItems.length === 0 ? (
          <p className="text-gray-500 py-2 italic text-sm">{t('customer.emptyCart')}</p>
        ) : (
          <>
            <div className="divide-y">
              {unsubmittedItems.map(item => (
                <div key={item.id} className="py-3 flex justify-between">
                  <div>
                    <p className="font-medium text-gray-600">{item.name}</p>
                    <div className="flex items-center mt-1">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1, tableId)}
                        disabled={item.quantity <= 1}
                        className="text-gray-600 hover:text-gray-800 disabled:opacity-50"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                        </svg>
                      </button>
                      <span className="mx-2 text-gray-600">{item.quantity}</span>
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1, tableId)}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className="font-medium text-gray-600">${(item.price * item.quantity).toFixed(2)}</span>
                    <button
                      onClick={() => removeFromCart(item.id, tableId)}
                      className="text-red-500 hover:text-red-700 text-sm mt-1"
                    >
                      {t('common.remove')}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 pt-4 border-t">
              <div className="flex justify-between font-semibold text-gray-600">
                <span>{t('common.total')}</span>
                <span>${calculateTotal().toFixed(2)}</span>
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={isSubmitting}
                className="mt-4 w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium disabled:opacity-50"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('common.submit')}
                  </span>
                ) : (
                  t('common.submit')
                )}
              </button>
            </div>
          </>
        )}
      </div>

      {/* 已提交的订单部分 - 无论购物车是否为空都显示 */}
      {submittedItems.length > 0 ? (
        <div className="pt-4 border-t border-dashed">
          <h3 className="font-medium text-gray-900 mb-3 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            {t('customer.submittedItems')}
          </h3>

          {/* 按订单ID分组显示 */}
          {Object.entries(
            submittedItems.reduce((acc, item) => {
              // const orderId = item.orderId || 'unknown';
              const groupId: string = item.groupId || 'unknown';

              if (!acc[groupId]) {
                acc[groupId] = [];
              }
              acc[groupId].push(item);
              return acc;
            }, {} as Record<string, CartItem[]>)
          ).map(([groupId, items]) => (
            <div key={groupId} className="mb-4 bg-gray-50 p-3 rounded-md border border-gray-100">
              <div className="border-b border-gray-200 pb-2">{groupId}</div>
              <div className="divide-y divide-gray-200">
                {items.map(item => (
                  <div key={`${groupId}-${item.id}`} className="py-2 flex justify-between">
                    <div>
                      <p className="font-medium text-gray-600">{item.name}</p>
                      <p className="text-sm text-gray-600">{t('customer.quantity')}: {item.quantity}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-2 pt-2 border-t border-gray-200 flex justify-between text-sm">
                <span className="font-medium text-gray-600">{t('common.total')}:</span>
                <span className="font-medium text-gray-600">
                  ${items.reduce((total, item) => total + (item.price * item.quantity), 0).toFixed(2)}
                </span>
              </div>
            </div>
          ))}
        </div>
      ) : unsubmittedItems.length === 0 ? (
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="mt-2 text-gray-500">{t('customer.noOrders')}</p>
          <p className="text-sm text-gray-400 mt-1">{t('customer.browseMenu')}</p>
        </div>
      ) : null}
    </div>
  );
}
