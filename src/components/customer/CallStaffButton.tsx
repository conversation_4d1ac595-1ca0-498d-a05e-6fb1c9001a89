'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { apiPost } from '@/lib/api';

interface CallStaffButtonProps {
  tableId: string;
}

export default function CallStaffButton({ tableId }: CallStaffButtonProps) {
  const t = useTranslations();
  const [isLoading, setIsLoading] = useState(false);
  const [isCalled, setIsCalled] = useState(false);

  const handleCallStaff = async () => {
    if (isLoading || isCalled) return;

    try {
      setIsLoading(true);

      // 获取认证token
      const token = localStorage.getItem('auth_token');
      if (!token) {
        alert(t('customer.sessionExpired'));
        return;
      }

      console.log('🔔 Calling staff for table:', tableId);

      // 调用API
      const response = await apiPost('/customer/call-staff', {
        token,
        tableId
      });

      if (response.success) {
        console.log('✅ Staff call sent successfully');
        setIsCalled(true);

        // 5秒后恢复按钮
        setTimeout(() => {
          setIsCalled(false);
        }, 5000);
      } else {
        throw new Error(response.error || 'Failed to call staff');
      }

    } catch (error: any) {
      console.error('Error calling staff:', error);
      alert(error.message || t('customer.callStaffError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleCallStaff}
      disabled={isLoading || isCalled}
      className={`
        flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
        ${isCalled 
          ? 'bg-green-100 text-green-700 cursor-not-allowed' 
          : isLoading
          ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
          : 'bg-orange-500 text-white hover:bg-orange-600 active:bg-orange-700'
        }
      `}
    >
      {/* 铃铛图标 */}
      <svg 
        className={`w-5 h-5 ${isCalled ? 'text-green-600' : isLoading ? 'text-gray-400' : 'text-white'}`}
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-7A2.5 2.5 0 016.5 7h11A2.5 2.5 0 0120 9.5v7a2.5 2.5 0 01-2.5 2.5H13" 
        />
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M15 7a3 3 0 11-6 0c0-1.5.5-3 3-3s3 1.5 3 3z" 
        />
      </svg>

      {/* 按钮文字 */}
      <span>
        {isCalled 
          ? t('customer.staffCalled')
          : isLoading 
          ? t('customer.calling')
          : t('customer.callStaff')
        }
      </span>

      {/* 加载动画 */}
      {isLoading && (
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
      )}
    </button>
  );
}
