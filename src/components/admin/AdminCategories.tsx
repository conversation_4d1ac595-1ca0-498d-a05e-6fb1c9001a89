'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { apiGet, apiPost, apiDelete, apiPut } from '@/lib/api';

type Restaurant = {
  id: string;
  name: string;
};

type Category = {
  id: string;
  restaurant_id: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  name_zh: string;
  created_at: string;
};

type NewCategory = {
  restaurant_id: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  name_zh: string;
};

export default function AdminCategories() {
  const t = useTranslations();
  const locale = useLocale() as string;
  const [categories, setCategories] = useState<Category[]>([]);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [selectedRestaurant, setSelectedRestaurant] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [newCategory, setNewCategory] = useState<NewCategory>({
    restaurant_id: '',
    name_en: '',
    name_ja: '',
    name_ko: '',
    name_zh: ''
  });
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  useEffect(() => {
    fetchRestaurants();
  }, []);

  useEffect(() => {
    if (selectedRestaurant) {
      fetchCategories(selectedRestaurant);
    }
  }, [selectedRestaurant]);

  const fetchRestaurants = async () => {
    try {
      const { data, error } = await supabase
        .from('restaurants')
        .select('*')
        .order('name');

      if (error) throw error;

      setRestaurants(data || []);

      if (data && data.length > 0) {
        setSelectedRestaurant(data[0].id);
        setNewCategory(prev => ({ ...prev, restaurant_id: data[0].id }));
      }
    } catch (error: any) {
      console.error('Error fetching restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async (restaurantId: string) => {
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('restaurant_id', restaurantId)
        .order('name_en');

      if (error) throw error;

      setCategories(data || []);
    } catch (error: any) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategory.name_ja.trim()) return;

    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          restaurant_id: selectedRestaurant,
          name_en: newCategory.name_en.trim() || newCategory.name_ja.trim(),
          name_ja: newCategory.name_ja.trim(),
          name_ko: newCategory.name_ko.trim() || newCategory.name_ja.trim(),
          name_zh: newCategory.name_zh.trim() || newCategory.name_ja.trim()
        })
        .select()
        .single();

      if (error) throw error;

      setCategories([...categories, data]);
      setNewCategory({
        restaurant_id: selectedRestaurant,
        name_en: '',
        name_ja: '',
        name_ko: '',
        name_zh: ''
      });
      setIsAddingCategory(false);
    } catch (error: any) {
      console.error('Error adding category:', error);
    }
  };

  const handleUpdateCategory = async () => {
    if (!editingCategory || !editingCategory.name_ja.trim()) return;

    try {
      const { error } = await supabase
        .from('categories')
        .update({
          name_en: editingCategory.name_en.trim() || editingCategory.name_ja.trim(),
          name_ja: editingCategory.name_ja.trim(),
          name_ko: editingCategory.name_ko.trim() || editingCategory.name_ja.trim(),
          name_zh: editingCategory.name_zh.trim() || editingCategory.name_ja.trim()
        })
        .eq('id', editingCategory.id);

      if (error) throw error;

      setCategories(categories.map(c =>
        c.id === editingCategory.id ? editingCategory : c
      ));
      setEditingCategory(null);
    } catch (error: any) {
      console.error('Error updating category:', error);
    }
  };

  const handleDeleteCategory = async (id: string) => {
    if (!confirm(t('admin.deleteCategory') + '?')) return;

    try {
      const { error } = await supabase
        .from('categories')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setCategories(categories.filter(c => c.id !== id));
    } catch (error: any) {
      console.error('Error deleting category:', error);
    }
  };

  const handleRestaurantChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const restaurantId = e.target.value;
    setSelectedRestaurant(restaurantId);
    setNewCategory(prev => ({ ...prev, restaurant_id: restaurantId }));
  };

  if (loading && restaurants.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{t('admin.categories')}</h2>
        <button
          onClick={() => setIsAddingCategory(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          {t('admin.addCategory')}
        </button>
      </div>

      <div className="mb-6">
        <label htmlFor="restaurant-select" className="block text-sm font-medium text-gray-700 mb-1">
          {t('common.restaurant')}
        </label>
        <select
          id="restaurant-select"
          value={selectedRestaurant}
          onChange={handleRestaurantChange}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
        >
          {restaurants.map((restaurant) => (
            <option key={restaurant.id} value={restaurant.id}>
              {restaurant.name}
            </option>
          ))}
        </select>
      </div>

      {isAddingCategory && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-lg font-medium mb-4">{t('admin.addCategory')}</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.japanese')} ({t('common.name')})
              </label>
              <input
                type="text"
                value={newCategory.name_ja}
                onChange={(e) => setNewCategory({ ...newCategory, name_ja: e.target.value })}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="日本語名"
              />
              <p className="mt-1 text-sm text-gray-500">
                {t('common.japanese')} {t('common.name')} is required. Other languages will be auto-translated if left empty.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.english')} ({t('common.name')})
              </label>
              <input
                type="text"
                value={newCategory.name_en}
                onChange={(e) => setNewCategory({ ...newCategory, name_en: e.target.value })}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="English name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.korean')} ({t('common.name')})
              </label>
              <input
                type="text"
                value={newCategory.name_ko}
                onChange={(e) => setNewCategory({ ...newCategory, name_ko: e.target.value })}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="한국어 이름"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('common.chinese')} ({t('common.name')})
              </label>
              <input
                type="text"
                value={newCategory.name_zh}
                onChange={(e) => setNewCategory({ ...newCategory, name_zh: e.target.value })}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="中文名称"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setIsAddingCategory(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium"
              >
                {t('common.cancel')}
              </button>
              <button
                onClick={handleAddCategory}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {t('common.save')}
              </button>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : categories.length === 0 ? (
        <p className="text-gray-500 py-4">No categories found for this restaurant.</p>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {categories.map((category) => (
              <li key={category.id}>
                <div className="px-4 py-4">
                  {editingCategory?.id === category.id ? (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('common.japanese')} ({t('common.name')})
                        </label>
                        <input
                          type="text"
                          value={editingCategory.name_ja}
                          onChange={(e) => setEditingCategory({ ...editingCategory, name_ja: e.target.value })}
                          className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('common.english')} ({t('common.name')})
                        </label>
                        <input
                          type="text"
                          value={editingCategory.name_en}
                          onChange={(e) => setEditingCategory({ ...editingCategory, name_en: e.target.value })}
                          className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('common.korean')} ({t('common.name')})
                        </label>
                        <input
                          type="text"
                          value={editingCategory.name_ko}
                          onChange={(e) => setEditingCategory({ ...editingCategory, name_ko: e.target.value })}
                          className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('common.chinese')} ({t('common.name')})
                        </label>
                        <input
                          type="text"
                          value={editingCategory.name_zh}
                          onChange={(e) => setEditingCategory({ ...editingCategory, name_zh: e.target.value })}
                          className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => setEditingCategory(null)}
                          className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-1 rounded-md text-sm"
                        >
                          {t('common.cancel')}
                        </button>
                        <button
                          onClick={handleUpdateCategory}
                          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                        >
                          {t('common.save')}
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex justify-between">
                        <h3 className="text-lg font-medium">{category[`name_${locale}`] || category.name_en}</h3>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setEditingCategory(category)}
                            className="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            {t('common.edit')}
                          </button>
                          <button
                            onClick={() => handleDeleteCategory(category.id)}
                            className="text-red-600 hover:text-red-900 text-sm"
                          >
                            {t('common.delete')}
                          </button>
                        </div>
                      </div>
                      <div className="mt-2 grid grid-cols-2 gap-2">
                        <div>
                          <span className="text-sm font-medium text-gray-500">{t('common.english')}:</span>
                          <span className="ml-1 text-sm text-gray-900">{category.name_en}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">{t('common.japanese')}:</span>
                          <span className="ml-1 text-sm text-gray-900">{category.name_ja}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">{t('common.korean')}:</span>
                          <span className="ml-1 text-sm text-gray-900">{category.name_ko}</span>
                        </div>
                        <div>
                          <span className="text-sm font-medium text-gray-500">{t('common.chinese')}:</span>
                          <span className="ml-1 text-sm text-gray-900">{category.name_zh}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
