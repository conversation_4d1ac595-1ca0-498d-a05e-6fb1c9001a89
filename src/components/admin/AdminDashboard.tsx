'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import AdminCategories from './AdminCategories';
// import AdminDishes from './AdminDishes';
import AdminRestaurants from './AdminRestaurants';

export default function AdminDashboard() {
  const t = useTranslations('admin');
  const [activeTab, setActiveTab] = useState<'restaurants' | 'categories' | 'dishes'>('restaurants');

  return (
    <div>
      <div className="mb-6 border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('restaurants')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'restaurants'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('restaurants')}
          </button>
          <button
            onClick={() => setActiveTab('categories')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'categories'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('categories')}
          </button>
          <button
            onClick={() => setActiveTab('dishes')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'dishes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('dishes')}
          </button>
          <a
            href="/admin/qr-generator"
            className="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            QR Generator
          </a>
        </nav>
      </div>

      <div>
        {activeTab === 'restaurants' && <AdminRestaurants />}
        {activeTab === 'categories' && <AdminCategories />}
        {/* {activeTab === 'dishes' && <AdminDishes />} */}
      </div>
    </div>
  );
}
