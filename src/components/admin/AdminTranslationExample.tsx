/**
 * Example component showing how to use admin translations
 * This demonstrates the usage of the new admin i18n system
 */

'use client';

import { useEffect, useState } from 'react';
import { getAdminMessages } from '@/lib/admin-i18n';

interface AdminTranslationExampleProps {
  locale: string;
}

export default function AdminTranslationExample({ locale }: AdminTranslationExampleProps) {
  const [messages, setMessages] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const adminMessages = await getAdminMessages(locale);
        setMessages(adminMessages);
      } catch (error) {
        console.error('Failed to load admin messages:', error);
      } finally {
        setLoading(false);
      }
    };

    loadMessages();
  }, [locale]);

  if (loading) {
    return <div>Loading admin translations...</div>;
  }

  if (!messages) {
    return <div>Failed to load admin translations</div>;
  }

  const { admin } = messages;

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-4">{admin.title}</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Analytics Section */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">{admin.analytics.title}</h2>
          <div className="space-y-2">
            <div><strong>{admin.analytics.selectRestaurant}:</strong> Restaurant A</div>
            <div><strong>{admin.analytics.startDate}:</strong> 2025-01-01</div>
            <div><strong>{admin.analytics.endDate}:</strong> 2025-01-31</div>
            <div><strong>{admin.analytics.analysisType}:</strong> {admin.analytics.summary}</div>
          </div>
          
          <div className="mt-4 grid grid-cols-3 gap-2 text-sm">
            <button className="bg-blue-500 text-white px-3 py-1 rounded">
              {admin.analytics.dishes}
            </button>
            <button className="bg-green-500 text-white px-3 py-1 rounded">
              {admin.analytics.tables}
            </button>
            <button className="bg-purple-500 text-white px-3 py-1 rounded">
              {admin.analytics.time}
            </button>
          </div>
        </div>

        {/* Restaurant Management Section */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">{admin.restaurants}</h2>
          <div className="space-y-2">
            <div><strong>{admin.restaurant.name}:</strong> Sample Restaurant</div>
            <div><strong>{admin.restaurant.owner}:</strong> John Doe</div>
            <div><strong>{admin.restaurant.status}:</strong> 
              <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-sm">
                {admin.restaurant.active}
              </span>
            </div>
            <div><strong>{admin.restaurant.createdAt}:</strong> 2025-01-01</div>
          </div>
          
          <div className="mt-4 flex gap-2">
            <button className="bg-blue-500 text-white px-3 py-1 rounded text-sm">
              {admin.restaurant.view}
            </button>
            <button className="bg-yellow-500 text-white px-3 py-1 rounded text-sm">
              {admin.restaurant.edit}
            </button>
            <button className="bg-red-500 text-white px-3 py-1 rounded text-sm">
              {admin.restaurant.delete}
            </button>
          </div>
        </div>

        {/* User Management Section */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">{admin.users}</h2>
          <div className="space-y-2">
            <div><strong>{admin.user.email}:</strong> <EMAIL></div>
            <div><strong>{admin.user.role}:</strong> Admin</div>
            <div><strong>{admin.user.restaurant}:</strong> Sample Restaurant</div>
            <div><strong>{admin.user.createdAt}:</strong> 2025-01-01</div>
            <div><strong>{admin.user.lastLogin}:</strong> 2025-01-15</div>
          </div>
        </div>

        {/* Common Actions Section */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">{admin.common.dashboard}</h2>
          <div className="grid grid-cols-2 gap-2">
            <button className="bg-gray-500 text-white px-3 py-2 rounded text-sm">
              {admin.common.search}
            </button>
            <button className="bg-gray-500 text-white px-3 py-2 rounded text-sm">
              {admin.common.filter}
            </button>
            <button className="bg-blue-500 text-white px-3 py-2 rounded text-sm">
              {admin.common.export}
            </button>
            <button className="bg-green-500 text-white px-3 py-2 rounded text-sm">
              {admin.common.refresh}
            </button>
          </div>
          
          <div className="mt-4 flex gap-2">
            <button className="bg-blue-600 text-white px-4 py-2 rounded">
              {admin.common.save}
            </button>
            <button className="bg-gray-400 text-white px-4 py-2 rounded">
              {admin.common.cancel}
            </button>
          </div>
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Current Locale:</strong> {locale}</p>
        <p><strong>Translation Source:</strong> src/i18n/admin/{locale}.json</p>
      </div>
    </div>
  );
}
