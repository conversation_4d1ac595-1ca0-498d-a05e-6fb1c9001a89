'use client';

import { ReactNode } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const t = useTranslations('admin');
  const pathname = usePathname();
  
  // 获取当前语言
  const locale = pathname.split('/')[1];
  
  // 导航项
  const navigation = [
    { name: t('dashboard'), href: `/${locale}/admin`, current: pathname === `/${locale}/admin` },
    { name: 'QR Generator', href: `/${locale}/admin/qr-generator`, current: pathname === `/${locale}/admin/qr-generator` },
    { name: t('analytics.title'), href: `/${locale}/admin/analytics`, current: pathname === `/${locale}/admin/analytics` },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-xl font-semibold text-gray-900 mr-8">
            Admin Dashboard
          </h1>
          <nav className="flex space-x-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  item.current
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
        <LanguageSwitcher />
      </header>

      <main>
        {children}
      </main>
    </div>
  );
}
