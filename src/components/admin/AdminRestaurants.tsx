'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { apiGet, apiPost, apiDelete, apiPut } from '@/lib/api';

type Restaurant = {
  id: string;
  name: string;
  created_at: string;
};

export default function AdminRestaurants() {
  const t = useTranslations();
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddingRestaurant, setIsAddingRestaurant] = useState(false);
  const [newRestaurantName, setNewRestaurantName] = useState('');
  const [editingRestaurant, setEditingRestaurant] = useState<Restaurant | null>(null);

  useEffect(() => {
    fetchRestaurants();
  }, []);

  const fetchRestaurants = async () => {
    setLoading(true);

    try {
      const data = await apiGet<Restaurant[]>('/restaurants/all');
      setRestaurants(data || []);
    } catch (error: any) {
      console.error('Error fetching restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddRestaurant = async () => {
    if (!newRestaurantName.trim()) return;

    try {
      const data = await apiPost<Restaurant>('/restaurants/create', {
        name: newRestaurantName.trim()
      });

      setRestaurants([...restaurants, data]);
      setNewRestaurantName('');
      setIsAddingRestaurant(false);
    } catch (error: any) {
      console.error('Error adding restaurant:', error);
    }
  };

  const handleUpdateRestaurant = async () => {
    if (!editingRestaurant || !editingRestaurant.name.trim()) return;

    try {
      const data = await apiPut<Restaurant>(`/restaurants/${editingRestaurant.id}`, {
        name: editingRestaurant.name.trim()
      });

      setRestaurants(restaurants.map(r =>
        r.id === editingRestaurant.id ? data : r
      ));
      setEditingRestaurant(null);
    } catch (error: any) {
      console.error('Error updating restaurant:', error);
    }
  };

  const handleDeleteRestaurant = async (id: string) => {
    if (!confirm(t('admin.deleteRestaurant') + '?')) return;

    try {
      await apiDelete(`/restaurants/${id}`);
      setRestaurants(restaurants.filter(r => r.id !== id));
    } catch (error: any) {
      console.error('Error deleting restaurant:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{t('admin.restaurants')}</h2>
        <button
          onClick={() => setIsAddingRestaurant(true)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          {t('admin.addRestaurant')}
        </button>
      </div>

      {isAddingRestaurant && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md">
          <h3 className="text-lg font-medium mb-2">{t('admin.addRestaurant')}</h3>
          <div className="flex">
            <input
              type="text"
              value={newRestaurantName}
              onChange={(e) => setNewRestaurantName(e.target.value)}
              placeholder={t('common.name')}
              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              onClick={handleAddRestaurant}
              className="ml-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              {t('common.save')}
            </button>
            <button
              onClick={() => setIsAddingRestaurant(false)}
              className="ml-2 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium"
            >
              {t('common.cancel')}
            </button>
          </div>
        </div>
      )}

      {restaurants.length === 0 ? (
        <p className="text-gray-500 py-4">No restaurants found.</p>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {restaurants.map((restaurant) => (
              <li key={restaurant.id}>
                <div className="px-4 py-4 flex items-center justify-between">
                  {editingRestaurant?.id === restaurant.id ? (
                    <div className="flex flex-1">
                      <input
                        type="text"
                        value={editingRestaurant.name}
                        onChange={(e) => setEditingRestaurant({ ...editingRestaurant, name: e.target.value })}
                        className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={handleUpdateRestaurant}
                        className="ml-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md text-sm"
                      >
                        {t('common.save')}
                      </button>
                      <button
                        onClick={() => setEditingRestaurant(null)}
                        className="ml-2 bg-gray-200 hover:bg-gray-300 text-gray-800 px-3 py-1 rounded-md text-sm"
                      >
                        {t('common.cancel')}
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{restaurant.name}</p>
                      </div>
                      <div className="ml-4 flex-shrink-0 flex">
                        <button
                          onClick={() => setEditingRestaurant(restaurant)}
                          className="mr-2 text-blue-600 hover:text-blue-900 text-sm"
                        >
                          {t('common.edit')}
                        </button>
                        <button
                          onClick={() => handleDeleteRestaurant(restaurant.id)}
                          className="text-red-600 hover:text-red-900 text-sm"
                        >
                          {t('common.delete')}
                        </button>
                      </div>
                    </>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
