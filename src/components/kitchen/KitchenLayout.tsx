'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { apiPost, apiDelete } from '@/lib/api';
import { AnimatePresence, motion } from 'framer-motion';
import StaffCallNotifications from './StaffCallNotifications';

type KitchenLayoutProps = {
  children: ReactNode;
};

export default function KitchenLayout({ children }: KitchenLayoutProps) {
  const t = useTranslations('kitchen');
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前语言
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // 导航链接数据
  const navLinks = [
    { href: `/${locale}/kitchen`, label: t('orders') },
    { href: `/${locale}/kitchen/qr-codes`, label: t('qrCodes') },
    { href: `/${locale}/kitchen/tables`, label: t('tableManagement') },
    { href: `/${locale}/kitchen/manual-order`, label: t('manualOrder') },
    { href: `/${locale}/kitchen/categories`, label: t('categoryManagement') },
    { href: `/${locale}/kitchen/dishes`, label: t('dishManagement') }
  ];

  // 关闭菜单
  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  // 点击导航链接时关闭菜单
  const handleNavLinkClick = () => {
    closeMenu();
  };

  // 设置定时器，定期刷新令牌
  useEffect(() => {
    const refreshTimer = setInterval(async () => {
      try {
        const response = await apiPost('/auth/refresh');
        if (!response.success) {
          console.error('KitchenLayout - 刷新会话失败:', response.error);
        } else {
          console.log('KitchenLayout - 会话刷新成功');
        }
      } catch (e) {
        console.error('KitchenLayout - 刷新会话错误:', e);
      }
    }, 30 * 60 * 1000); // 每30分钟刷新一次

    return () => clearInterval(refreshTimer);
  }, []);

  // 点击页面其他区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isMenuOpen && !target.closest('.mobile-menu') && !target.closest('.menu-button')) {
        closeMenu();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // 移动端菜单动画
  const menuVariants = {
    closed: {
      x: '-100%',
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    },
    open: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  // 背景遮罩动画
  const overlayVariants = {
    closed: {
      opacity: 0,
      transition: {
        duration: 0.3
      }
    },
    open: {
      opacity: 1,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <header className="bg-white shadow-sm py-4 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            {/* 移动端菜单按钮 */}
            <button
              className="menu-button md:hidden mr-4 text-gray-600 focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <h1 className="text-xl font-semibold text-gray-900 mr-4">
              {t('title')}
            </h1>

            {/* 桌面端导航 - 在中等屏幕及以上显示 */}
            <nav className="hidden md:flex ml-6 space-x-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname === link.href
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {link.label}
                </Link>
              ))}
            </nav>
          </div>

          <div className="flex items-center space-x-4">
            {/* 店员呼叫通知 */}
            <StaffCallNotifications />

            <button
              onClick={() => {
                try {
                  apiDelete('/auth');
                  localStorage.clear();
                  router.replace(`/${locale}/login`);
                } catch (error: any) {
                  console.error('登出失败:', error);
                }
              }}
              className="flex text-sm text-gray-600 hover:bg-gray-100 mr-2 rounded-md p-2"
            >
              {t('logout') || t('signOut')}
            </button>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* 移动端侧边菜单 */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
              initial="closed"
              animate="open"
              exit="closed"
              variants={overlayVariants}
              onClick={closeMenu}
            />

            {/* 侧边菜单 */}
            <motion.div
              className="mobile-menu fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-50 md:hidden overflow-y-auto"
              initial="closed"
              animate="open"
              exit="closed"
              variants={menuVariants}
            >
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">{t('title')}</h2>
                  <button
                    className="text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={closeMenu}
                    aria-label="Close menu"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <nav className="p-4">
                <ul className="space-y-2">
                  {navLinks.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className={`block px-4 py-2 rounded-md text-sm font-medium ${
                          pathname === link.href
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                        onClick={handleNavLinkClick}
                      >
                        {link.label}
                      </Link>
                    </li>
                  ))}
                </ul>
              </nav>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <main className="flex-grow">
        {children}
      </main>
    </div>
  );
}
