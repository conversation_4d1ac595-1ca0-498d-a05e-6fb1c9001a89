'use client';

import { useState, useEffect, useRef } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import OptimisticKitchenOrderCard from './OptimisticKitchenOrderCard';

type OrderItem = {
  id: string;
  dish_id: string;
  dish_name: string;
  dishes:{
    name_zh:string;
    name_ja:string;
    name_en:string;
    name_ko:string;
  };
  quantity: number;
  price: number;
  status: string;
};

type Order = {
  id: string;
  table_id: string;
  // table_number: string;
  tables: {table_number: string};
  status: string;
  created_at: string;
  items: OrderItem[];
};

export default function KitchenOrderList() {
  const t = useTranslations();
  const locale = useLocale() as string;
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  // 添加一个状态来显示新订单通知
  const [showNotification, setShowNotification] = useState(false);
  // 添加Tab状态
  const [activeTab, setActiveTab] = useState<string>('all');
  // SSE连接引用
  const eventSourceRef = useRef<EventSource | null>(null);
  // 初始加载标志
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  // 初始加载订单数据
  const fetchInitialOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/kitchen/orders');

      if (!response.ok) {
        throw new Error(`Error fetching orders: ${response.statusText}`);
      }

      const ordersData = await response.json();
      setOrders(ordersData || []);
      setLoading(false);
      setIsFirstLoad(false);
    } catch (error: any) {
      console.error('Error fetching initial orders:', error);
      setLoading(false);
      setIsFirstLoad(false);
    }
  };

  // 建立SSE连接
  const connectSSE = () => {
    try {
      setConnectionStatus('connecting');

      // 创建EventSource连接
      const eventSource = new EventSource('/api/kitchen/sse');
      eventSourceRef.current = eventSource;

      // 连接成功
      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setConnectionStatus('connected');
      };

      // 接收消息
      eventSource.onmessage = (event) => {
        console.log('SSE message received:', event.data);
      };

      // 监听连接确认事件
      eventSource.addEventListener('connected', (event) => {
        console.log('SSE connected:', event.data);
        setConnectionStatus('connected');
      });

      // 监听新订单事件
      eventSource.addEventListener('order_created', (event) => {
        console.log('New order received via SSE:', event.data);
        const data = JSON.parse(event.data);

        // 更新订单列表
        setOrders(prevOrders => {
          // 检查订单是否已存在
          const existingOrder = prevOrders.find(order => order.id === data.order.id);
          if (existingOrder) {
            return prevOrders;
          }

          // 添加新订单到列表顶部
          return [data.order, ...prevOrders];
        });

        // 显示新订单通知
        setShowNotification(true);

        // 3秒后自动隐藏通知
        setTimeout(() => {
          setShowNotification(false);
        }, 3000);
      });

      // 监听订单更新事件
      eventSource.addEventListener('order_updated', (event) => {
        console.log('Order updated via SSE:', event.data);
        const data = JSON.parse(event.data);

        // 更新订单列表中的特定订单
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === data.order.id ? data.order : order
          )
        );
      });

      // 连接错误
      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        setConnectionStatus('disconnected');

        // EventSource会自动重连，但我们可以在这里添加额外的错误处理
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log('SSE connection closed, will attempt to reconnect...');
          // 可以在这里添加重连逻辑，但EventSource通常会自动重连
        }
      };

    } catch (error) {
      console.error('Error establishing SSE connection:', error);
      setConnectionStatus('disconnected');
    }
  };

  // 组件挂载时建立连接
  useEffect(() => {
    // 初始加载订单
    fetchInitialOrders();

    // 建立SSE连接
    connectSSE();

    // 组件卸载时清理连接
    return () => {
      if (eventSourceRef.current) {
        console.log('Closing SSE connection');
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, []);

  // 监听页面可见性变化，重新连接SSE
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' &&
          (!eventSourceRef.current || eventSourceRef.current.readyState === EventSource.CLOSED)) {
        console.log('Page became visible, reconnecting SSE...');
        connectSSE();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);



  const updateOrderStatus = async (orderId: string, newStatus: string): Promise<boolean> => {
    try {
      // 使用 API 更新订单状态
      const response = await fetch('/api/kitchen/orders/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId, status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order status: ${response.statusText}`);
      }

      // 不需要手动刷新，SSE会自动推送更新
      return true;
    } catch (error: any) {
      console.error('Error updating order status:', error);
      return false;
    }
  };

  const updateOrderItemStatus = async (itemId: string, newStatus: string): Promise<boolean> => {
    try {
      // 使用 API 更新订单项目状态
      const response = await fetch('/api/kitchen/order-items/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order item status: ${response.statusText}`);
      }

      // 不需要手动刷新，SSE会自动推送更新
      return true;
    } catch (error: any) {
      console.error('Error updating order item status:', error);
      return false;
    }
  };

  // 更新订单项目数量
  const updateOrderItemQuantity = async (itemId: string, quantity: number): Promise<boolean> => {
    try {
      // 使用 API 更新订单项目数量
      const response = await fetch('/api/kitchen/order-items/update-quantity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, quantity, action: 'update' }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order item quantity: ${response.statusText}`);
      }

      // 不需要手动刷新，SSE会自动推送更新
      return true;
    } catch (error: any) {
      console.error('Error updating order item quantity:', error);
      return false;
    }
  };

  // 取消订单项目
  const cancelOrderItem = async (itemId: string): Promise<boolean> => {
    try {
      // 使用 API 取消订单项目
      const response = await fetch('/api/kitchen/order-items/update-quantity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, action: 'cancel' }),
      });

      if (!response.ok) {
        throw new Error(`Error cancelling order item: ${response.statusText}`);
      }

      // 不需要手动刷新，SSE会自动推送更新
      return true;
    } catch (error: any) {
      console.error('Error cancelling order item:', error);
      return false;
    }
  };

  // 过滤掉没有订单项的订单
  const filteredOrders = orders.filter(order => order.items && order.items.length > 0);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Group orders by status
  const pendingOrders = filteredOrders.filter(order => order.status === 'pending');
  const inProgressOrders = filteredOrders.filter(order => order.status === 'in_progress');
  const awaitingPaymentOrders = filteredOrders.filter(order => order.status === 'awaiting_payment');
  const readyOrders = filteredOrders.filter(order => order.status === 'ready');
  const completedOrders = filteredOrders.filter(order => order.status === 'completed');

  // 根据当前Tab筛选订单
  const getFilteredOrdersByTab = () => {
    switch (activeTab) {
      case 'pending':
        return pendingOrders;
      case 'in_progress':
        return inProgressOrders;
      case 'ready':
        return readyOrders;
      case 'awaiting_payment':
        return awaitingPaymentOrders;
      case 'completed':
        return completedOrders;
      default:
        return filteredOrders;
    }
  };

  const currentOrders = getFilteredOrdersByTab();

  // Tab配置
  const tabs = [
    { id: 'all', label: t('kitchen.all'), count: filteredOrders.length },
    { id: 'pending', label: t('kitchen.newOrders'), count: pendingOrders.length },
    { id: 'in_progress', label: t('kitchen.inProgress'), count: inProgressOrders.length },
    { id: 'ready', label: t('kitchen.ready'), count: readyOrders.length },
    { id: 'awaiting_payment', label: t('kitchen.awaitingPayment'), count: awaitingPaymentOrders.length },
    { id: 'completed', label: t('kitchen.completed'), count: completedOrders.length },
  ];

  // 手动刷新数据（用于通知点击）
  const handleManualRefresh = async () => {
    try {
      // 隐藏通知
      setShowNotification(false);

      // 重新获取数据以确保同步
      const response = await fetch('/api/kitchen/orders');
      if (response.ok) {
        const ordersData = await response.json();
        setOrders(ordersData);
      }
    } catch (error: any) {
      console.error('Error refreshing orders:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* 新订单通知 */}
      {showNotification && (
        <div
          className="bg-blue-50 border-l-4 border-blue-500 p-4 cursor-pointer hover:bg-blue-100 transition-colors"
          onClick={handleManualRefresh}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                {t('kitchen.newOrdersAvailable')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Tab导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              <span>{tab.label}</span>
              {tab.count > 0 && (
                <span className={`
                  inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full
                  ${activeTab === tab.id
                    ? 'bg-blue-100 text-blue-600'
                    : 'bg-gray-100 text-gray-600'
                  }
                `}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* 订单内容 */}
      <div>
        {currentOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {activeTab === 'all' ? t('kitchen.noOrders') : `${t('kitchen.noOrders')} - ${tabs.find(tab => tab.id === activeTab)?.label}`}
            </h3>
            <p className="text-gray-500">
              {activeTab === 'pending' && t('kitchen.noPendingOrders')}
              {activeTab === 'in_progress' && t('kitchen.noInProgressOrders')}
              {activeTab === 'ready' && t('kitchen.noReadyOrders')}
              {activeTab === 'awaiting_payment' && t('kitchen.noAwaitingPaymentOrders')}
              {activeTab === 'completed' && t('kitchen.noCompletedOrders')}
              {activeTab === 'all' && t('kitchen.noOrdersDescription')}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {currentOrders.map(order => (
              <OptimisticKitchenOrderCard
                key={order.id}
                order={order}
                onUpdateOrderStatus={updateOrderStatus}
                onUpdateItemStatus={updateOrderItemStatus}
                onUpdateItemQuantity={updateOrderItemQuantity}
                onCancelItem={cancelOrderItem}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
