'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import OptimisticKitchenOrderCard from './OptimisticKitchenOrderCard';

type OrderItem = {
  id: string;
  dish_id: string;
  dish_name: string;
  dishes:{
    name_zh:string;
    name_ja:string;
    name_en:string;
    name_ko:string;
  };
  quantity: number;
  price: number;
  status: string;
};

type Order = {
  id: string;
  table_id: string;
  // table_number: string;
  tables: {table_number: string};
  status: string;
  created_at: string;
  items: OrderItem[];
};

export default function KitchenOrderList() {
  const t = useTranslations();
  const locale = useLocale() as string;
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshKey] = useState(0); // 用于手动刷新的key
  // 添加一个状态来跟踪是否是初始加载
  // const [isFirstLoad, c] = useState(true);
  let isFirstLoad = true;
  // 添加一个状态来显示新订单通知
  const [showNotification, setShowNotification] = useState(false);

  // 使用HTTP请求获取订单数据
  const fetchOrders = async () => {
    try {
      // 只在第一次加载时显示加载状态
      if (isFirstLoad) {
        setLoading(true);
      }

      // 使用 API 获取订单数据
      const response = await fetch('/api/kitchen/orders');

      if (!response.ok) {
        throw new Error(`Error fetching orders: ${response.statusText}`);
      }

      const ordersData = await response.json();

      if (!ordersData || ordersData.length === 0) {
        setOrders([]);
        if (isFirstLoad) {
          setLoading(false);
          // setIsFirstLoad(false);
          isFirstLoad = false;// 标记初始加载已完成
        }
        return;
      }

      setOrders(ordersData);
      // 隐藏通知
      setShowNotification(false);

      // 标记初始加载已完成
      if (isFirstLoad) {
        // setIsFirstLoad(false);
        isFirstLoad = false;

      }

    } catch (error: any) {
      console.error('Error fetching orders:', error);
    } finally {
      // 只在初始加载时重置加载状态
      if (loading) {
        setLoading(false);
      }
    }
  };
  
  useEffect(() => {
    // 初始加载数据
    fetchOrders();

    // 设置轮询间隔，定期检查是否有新数据
    const interval = setInterval(() => {
      fetchOrders();
    }, 10000); // 每10秒检查一次是否需要获取数据

    return () => {
      clearInterval(interval);
    };
  }, [locale, refreshKey]);

  

  const updateOrderStatus = async (orderId: string, newStatus: string): Promise<boolean> => {
    try {
      // 使用 API 更新订单状态
      const response = await fetch('/api/kitchen/orders/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId, status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order status: ${response.statusText}`);
      }

      // 静默刷新数据，不显示加载状态
      try {
        const response = await fetch('/api/kitchen/orders');
        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      } catch (error: any) {
        console.error('Error refreshing orders:', error);
      }
      return true;
    } catch (error: any) {
      console.error('Error updating order status:', error);
      return false;
    }
  };

  const updateOrderItemStatus = async (itemId: string, newStatus: string): Promise<boolean> => {
    try {
      // 使用 API 更新订单项目状态
      const response = await fetch('/api/kitchen/order-items/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order item status: ${response.statusText}`);
      }

      // 静默刷新数据，不显示加载状态
      try {
        const response = await fetch('/api/kitchen/orders');
        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      } catch (error: any) {
        console.error('Error refreshing orders:', error);
      }
      return true;
    } catch (error: any) {
      console.error('Error updating order item status:', error);
      return false;
    }
  };

  // 更新订单项目数量
  const updateOrderItemQuantity = async (itemId: string, quantity: number): Promise<boolean> => {
    try {
      // 使用 API 更新订单项目数量
      const response = await fetch('/api/kitchen/order-items/update-quantity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, quantity, action: 'update' }),
      });

      if (!response.ok) {
        throw new Error(`Error updating order item quantity: ${response.statusText}`);
      }

      // 静默刷新数据，不显示加载状态
      try {
        const response = await fetch('/api/kitchen/orders');
        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      } catch (error: any) {
        console.error('Error refreshing orders:', error);
      }
      return true;
    } catch (error: any) {
      console.error('Error updating order item quantity:', error);
      return false;
    }
  };

  // 取消订单项目
  const cancelOrderItem = async (itemId: string): Promise<boolean> => {
    try {
      // 使用 API 取消订单项目
      const response = await fetch('/api/kitchen/order-items/update-quantity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ itemId, action: 'cancel' }),
      });

      if (!response.ok) {
        throw new Error(`Error cancelling order item: ${response.statusText}`);
      }

      // 静默刷新数据，不显示加载状态
      try {
        const response = await fetch('/api/kitchen/orders');
        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      } catch (error: any) {
        console.error('Error refreshing orders:', error);
      }
      return true;
    } catch (error: any) {
      console.error('Error cancelling order item:', error);
      return false;
    }
  };

  // 过滤掉没有订单项的订单
  const filteredOrders = orders.filter(order => order.items && order.items.length > 0);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (filteredOrders.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">{t('kitchen.noOrders')}</h3>
      </div>
    );
  }

  // Group orders by status
  const pendingOrders = filteredOrders.filter(order => order.status === 'pending');
  const inProgressOrders = filteredOrders.filter(order => order.status === 'in_progress');
  const awaitingPaymentOrders = filteredOrders.filter(order => order.status === 'awaiting_payment');

  // 手动刷新数据
  const handleManualRefresh = async () => {
    try {
      // 静默刷新数据，不显示加载状态
      const response = await fetch('/api/kitchen/orders');
      if (response.ok) {
        const ordersData = await response.json();
        setOrders(ordersData);
        // 隐藏通知
        setShowNotification(false);
      }
    } catch (error: any) {
      console.error('Error refreshing orders:', error);
    }
  };

  return (
    <div className="space-y-8">
      {/* 新订单通知 */}
      {showNotification && (
        <div
          className="bg-blue-50 border-l-4 border-blue-500 p-4 cursor-pointer hover:bg-blue-100 transition-colors"
          onClick={handleManualRefresh}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                {t('kitchen.newOrdersAvailable')}
              </p>
            </div>
          </div>
        </div>
      )}
      {/* Pending Orders */}
      <div>
        <h2 className="text-xl font-semibold mb-4">{t('kitchen.newOrders')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {pendingOrders.map(order => (
            <OptimisticKitchenOrderCard
              key={order.id}
              order={order}
              onUpdateOrderStatus={updateOrderStatus}
              onUpdateItemStatus={updateOrderItemStatus}
              onUpdateItemQuantity={updateOrderItemQuantity}
              onCancelItem={cancelOrderItem}
            />
          ))}
        </div>
      </div>

      {/* In Progress Orders */}
      {inProgressOrders.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">{t('kitchen.inProgress')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {inProgressOrders.map(order => (
              <OptimisticKitchenOrderCard
                key={order.id}
                order={order}
                onUpdateOrderStatus={updateOrderStatus}
                onUpdateItemStatus={updateOrderItemStatus}
                onUpdateItemQuantity={updateOrderItemQuantity}
                onCancelItem={cancelOrderItem}
              />
            ))}
          </div>
        </div>
      )}

      {/* Awaiting Payment Orders */}
      {awaitingPaymentOrders.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold mb-4">{t('kitchen.awaitingPayment')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {awaitingPaymentOrders.map(order => (
              <OptimisticKitchenOrderCard
                key={order.id}
                order={order}
                onUpdateOrderStatus={updateOrderStatus}
                onUpdateItemStatus={updateOrderItemStatus}
                onUpdateItemQuantity={updateOrderItemQuantity}
                onCancelItem={cancelOrderItem}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
