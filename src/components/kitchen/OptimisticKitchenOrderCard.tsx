'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { formatDistanceToNow } from 'date-fns';

type OrderItem = {
  id: string;
  dish_id: string;
  dish_name?: string;
  dishes:{
    name_zh:string;
    name_ja:string;
    name_en:string;
    name_ko:string;
  };
  quantity: number;
  price: number;
  status: string;
  item_type?: string;
};

type Order = {
  id: string;
  table_id: string;
  // table_number: string;
  tables:{table_number:string};
  status: string;
  created_at: string;
  items: OrderItem[];
};

type OptimisticKitchenOrderCardProps = {
  order: Order;
  onUpdateOrderStatus: (orderId: string, status: string) => Promise<boolean>;
  onUpdateItemStatus: (itemId: string, status: string) => Promise<boolean>;
  onUpdateItemQuantity: (itemId: string, quantity: number) => Promise<boolean>;
  onCancelItem: (itemId: string) => Promise<boolean>;
};

export default function OptimisticKitchenOrderCard({
  order: initialOrder,
  onUpdateOrderStatus,
  onUpdateItemStatus,
  onUpdateItemQuantity,
  onCancelItem
}: OptimisticKitchenOrderCardProps) {
  const t = useTranslations();
  // 使用本地状态来实现乐观更新
  const [order, setOrder] = useState<Order>(initialOrder);
  // 跟踪正在更新的项目，防止重复点击
  const [updatingItems, setUpdatingItems] = useState<Set<string>>(new Set());
  const [updatingOrder, setUpdatingOrder] = useState<boolean>(false);
  // 跟踪正在编辑数量的项目
  const [editingQuantityItem, setEditingQuantityItem] = useState<string | null>(null);
  // 存储编辑中的数量
  const [editingQuantity, setEditingQuantity] = useState<number>(1);

  // 获取状态的翻译键
  const getStatusTranslationKey = (status: string): string => {
    switch (status) {
      case 'in_progress':
        return 'inProgress';
      case 'awaiting_payment':
        return 'awaitingPayment';
      default:
        return status.replace(/_/g, '');
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'served':
        return 'bg-purple-100 text-purple-800';
      case 'awaiting_payment':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 打印订单函数
  const printOrder = () => {
    // 创建一个新的打印窗口
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups for this website');
      return;
    }

    // 确保餐桌号存在
    const tableNumber = order.tables?.table_number || '未知';
//状態: ${t(`status.${getStatusTranslationKey(order.status)}`)}
    // 准备打印内容 - 总单
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>订单 #${order.id.substring(0, 8)}</title>
        <meta charset="utf-8">
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            font-size: 12px;
          }
          .receipt {
            width: 80mm;
            margin: 0 auto;
            padding: 5mm;
            border: 1px solid #ccc;
            page-break-after: always;
          }
          .header {
            text-align: center;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
          }
          .info {
            margin-bottom: 10px;
          }
          .info div {
            margin-bottom: 5px;
          }
          .items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
          }
          .items th, .items td {
            border-bottom: 1px solid #ddd;
            padding: 5px;
            text-align: left;
          }
          .items th {
            font-weight: bold;
          }
          .total {
            text-align: right;
            font-weight: bold;
            margin-top: 10px;
          }
          .footer {
            text-align: center;
            margin-top: 10px;
            font-size: 10px;
          }
          @media print {
            body {
              margin: 0;
              padding: 0;
            }
            .receipt {
              border: none;
              width: 100%;
              padding: 0;
            }
          }
        </style>
      </head>
      <body>
        <!-- 总单 -->
        <div class="receipt">
          <div class="header">注文</div>
          <div class="info">
            <div>注文番号: #${order.id.substring(0, 8)}</div>
            <div>テーブル: ${tableNumber}</div>
            <div>日付: ${new Date(order.created_at).toLocaleString()}</div>
            
          </div>
          <table class="items">
            <thead>
              <tr>
                <th>料理</th>
                <th>数量</th>
                <th>単価</th>
                <th>小計</th>
              </tr>
            </thead>
            <tbody>
              ${order.items.map(item => `
                <tr>
                  <td>${item.dishes?.name_ja || item.dishes?.name_zh || ""}<br><span style="font-size:10px">${item.dishes?.name_zh || ''}</span></td>
                  <td>${item.quantity}</td>
                  <td>¥${item.price.toLocaleString()}</td>
                  <td>¥${(item.price * item.quantity).toLocaleString()}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
          <div class="total">
            合計: ¥${order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toLocaleString()}
          </div>
          <div class="footer">
            ありがとうございました!
          </div>
        </div>

        <!-- 单个菜品小票 -->
        ${order.items.map(item => `
          <div class="receipt">
            <div class="header">伝票</div>
            <div class="info">
              <div>注文番号: #${order.id.substring(0, 8)}</div>
              <div>テーブル: ${tableNumber}</div>
              <div>日付: ${new Date(order.created_at).toLocaleString()}</div>
              <div>タイプ: ${item.item_type === 'append' ? '追加' : '新規'}</div>
            </div>
            <div style="font-size: 16px; font-weight: bold; margin: 15px 0;">
              ${item.dishes?.name_ja || item.dishes?.name_zh || item.dish_name}
            </div>
            <div style="font-size: 14px; margin-bottom: 15px;">
              ${item.dishes?.name_zh || ''}
            </div>
            <div style="font-size: 14px; font-weight: bold;">
              数量: ${item.quantity}
            </div>
            <div class="footer">
              ありがとうございました!
            </div>
          </div>
        `).join('')}
      </body>
      </html>
    `;

    // 写入打印内容
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();

    // 等待图片加载完成后打印
    printWindow.onload = function() {
      printWindow.print();
      // 打印完成后关闭窗口
      // printWindow.close();
    };
  };

  const handleOrderStatusUpdate = async (newStatus: string) => {
    if (updatingOrder) return; // 防止重复点击

    // 保存当前状态以便回滚
    const previousStatus = order.status;

    try {
      setUpdatingOrder(true);

      // 乐观更新本地状态
      setOrder(prev => ({
        ...prev,
        status: newStatus
      }));

      // 如果是从 pending 到 in_progress 的状态变化，打印订单,
      // todo： 打印应该是不弹框。手动打印。
      if (previousStatus === 'pending' && newStatus === 'in_progress') {
        // printOrder();
      }

      // 调用API更新服务器
      const success = await onUpdateOrderStatus(order.id, newStatus);

      if (!success) {
        throw new Error('Failed to update order status');
      }
    } catch (error: any) {
      console.error('Error updating order status:', error);

      // 回滚到之前的状态
      setOrder(prev => ({
        ...prev,
        status: previousStatus
      }));

      // 显示错误消息
      alert(t('kitchen.errorUpdatingOrder'));
    } finally {
      setUpdatingOrder(false);
    }
  };

  const handleItemStatusUpdate = async (itemId: string, newStatus: string) => {
    if (updatingItems.has(itemId)) return; // 防止重复点击

    // 找到当前项目
    const currentItem = order.items.find(item => item.id === itemId);
    if (!currentItem) return;

    // 保存当前状态以便回滚
    const previousStatus = currentItem.status;

    try {
      // 标记项目正在更新
      setUpdatingItems(prev => new Set(prev).add(itemId));

      // 乐观更新本地状态
      setOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId ? { ...item, status: newStatus } : item
        )
      }));

      // 调用API更新服务器
      const success = await onUpdateItemStatus(itemId, newStatus);

      if (!success) {
        throw new Error('Failed to update item status');
      }

      // 如果所有项目都已上菜，自动更新订单状态
      const updatedItems = order.items.map(item =>
        item.id === itemId ? { ...item, status: newStatus } : item
      );

      const allItemsServed = updatedItems.every(item => item.status === 'served');

      if (allItemsServed && order.status === 'in_progress') {
        handleOrderStatusUpdate('awaiting_payment');
      }
    } catch (error: any) {
      console.error('Error updating item status:', error);

      // 回滚到之前的状态
      setOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId ? { ...item, status: previousStatus } : item
        )
      }));

      // 显示错误消息
      alert(t('kitchen.errorUpdatingOrderItem'));
    } finally {
      // 移除正在更新标记
      setUpdatingItems(prev => {
        const next = new Set(prev);
        next.delete(itemId);
        return next;
      });
    }
  };

  // 开始编辑菜品数量
  const startEditingQuantity = (itemId: string) => {
    // 找到当前项目
    const currentItem = order.items.find(item => item.id === itemId);
    if (!currentItem) return;

    // 设置当前编辑的项目和数量
    setEditingQuantityItem(itemId);
    setEditingQuantity(currentItem.quantity);
  };

  // 取消编辑菜品数量
  const cancelEditingQuantity = () => {
    setEditingQuantityItem(null);
    setEditingQuantity(1);
  };

  // 保存编辑的菜品数量
  const saveEditingQuantity = async (itemId: string) => {
    if (updatingItems.has(itemId)) return; // 防止重复点击

    // 找到当前项目
    const currentItem = order.items.find(item => item.id === itemId);
    if (!currentItem) return;

    // 如果数量没有变化，直接取消编辑
    if (currentItem.quantity === editingQuantity) {
      cancelEditingQuantity();
      return;
    }

    // 保存当前数量以便回滚
    const previousQuantity = currentItem.quantity;

    try {
      // 标记项目正在更新
      setUpdatingItems(prev => new Set(prev).add(itemId));

      // 乐观更新本地状态
      setOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId ? { ...item, quantity: editingQuantity } : item
        )
      }));

      // 调用API更新服务器
      const success = await onUpdateItemQuantity(itemId, editingQuantity);

      if (!success) {
        throw new Error('Failed to update item quantity');
      }

      // 取消编辑状态
      cancelEditingQuantity();
    } catch (error: any) {
      console.error('Error updating item quantity:', error);

      // 回滚到之前的状态
      setOrder(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.id === itemId ? { ...item, quantity: previousQuantity } : item
        )
      }));

      // 显示错误消息
      alert(t('kitchen.errorUpdatingOrderItemQuantity'));
    } finally {
      // 移除正在更新标记
      setUpdatingItems(prev => {
        const next = new Set(prev);
        next.delete(itemId);
        return next;
      });
    }
  };

  // 取消菜品
  const handleCancelItem = async (itemId: string) => {
    if (updatingItems.has(itemId)) return; // 防止重复点击

    // 找到当前项目
    const currentItem = order.items.find(item => item.id === itemId);
    if (!currentItem) return;

    // 确认取消
    if (!confirm(t('kitchen.confirmCancelItem'))) {
      return;
    }

    try {
      // 标记项目正在更新
      setUpdatingItems(prev => new Set(prev).add(itemId));

      // 乐观更新本地状态 - 从订单中移除该项目
      setOrder(prev => ({
        ...prev,
        items: prev.items.filter(item => item.id !== itemId)
      }));

      // 调用API更新服务器
      const success = await onCancelItem(itemId);

      if (!success) {
        throw new Error('Failed to cancel item');
      }
    } catch (error: any) {
      console.error('Error cancelling item:', error);

      // 回滚到之前的状态 - 将项目添加回订单
      setOrder(prev => ({
        ...prev,
        items: [...prev.items, currentItem].sort((a, b) =>
          a.id.localeCompare(b.id) // 保持原有顺序
        )
      }));

      // 显示错误消息
      alert(t('kitchen.errorCancellingOrderItem'));
    } finally {
      // 移除正在更新标记
      setUpdatingItems(prev => {
        const next = new Set(prev);
        next.delete(itemId);
        return next;
      });
    }
  };



  const getOrderActionButton = () => {
    // 检查订单是否为新订单（没有任何操作过的订单）
    const isNewOrder = order.status === 'pending' && order.items.every(item => item.status === 'pending');

    switch (order.status) {
      case 'pending':
        return (
          <div className="flex space-x-2">
            <button
              onClick={() => handleOrderStatusUpdate('in_progress')}
              disabled={updatingOrder}
              className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
                updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {t('kitchen.startCooking')}
            </button>
            {isNewOrder && (
              <button
                onClick={() => {
                  if (confirm(t('kitchen.confirmCancelOrder'))) {
                    handleOrderStatusUpdate('cancelled');
                  }
                }}
                disabled={updatingOrder}
                className={`bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
                  updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {t('kitchen.cancelOrder')}
              </button>
            )}
          </div>
        );
      case 'in_progress':
        // 检查是否有待处理的项目
        const hasPendingItems = order.items.some(item => item.status === 'pending');
        // 检查是否有制作中的项目
        const hasInProgressItems = order.items.some(item => item.status === 'in_progress');
        // 检查是否所有项目都已就绪或已上菜
        const allItemsReadyOrServed = order.items.every(
          item => item.status === 'ready' || item.status === 'served'
        );

        return (
          <div className="flex space-x-2">
            {hasPendingItems && (
              <button
                onClick={() => {
                  // 批量将所有待处理的项目更新为制作中
                  order.items.forEach(item => {
                    if (item.status === 'pending') {
                      handleItemStatusUpdate(item.id, 'in_progress');
                    }
                  });
                }}
                disabled={updatingOrder}
                className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
                  updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {t('kitchen.startCooking')}
              </button>
            )}

            {hasInProgressItems && (
              <button
                onClick={() => {
                  // 批量将所有制作中的项目更新为已就绪
                  order.items.forEach(item => {
                    if (item.status === 'in_progress') {
                      handleItemStatusUpdate(item.id, 'ready');
                    }
                  });
                }}
                disabled={updatingOrder}
                className={`bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
                  updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {t('kitchen.markAllInProgressAsReady')}
              </button>
            )}

            {allItemsReadyOrServed && (
              <button
                onClick={() => handleOrderStatusUpdate('awaiting_payment')}
                disabled={updatingOrder}
                className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
                  updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {t('kitchen.markAsAwaitingPayment')}
              </button>
            )}
          </div>
        );
      case 'awaiting_payment':
        return (
          <button
            onClick={() => handleOrderStatusUpdate('completed')}
            disabled={updatingOrder}
            className={`bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium ${
              updatingOrder ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {t('kitchen.markAsCompleted')}
          </button>
        );
      default:
        return null;
    }
  };

  const getItemActionButton = (item: OrderItem) => {
    const isUpdating = updatingItems.has(item.id);
    const isEditing = editingQuantityItem === item.id;

    // 如果正在编辑数量，显示编辑界面
    if (isEditing) {
      return (
        <div className="flex items-center space-x-2">
          <input
            type="number"
            min="1"
            value={editingQuantity}
            onChange={(e) => setEditingQuantity(Math.max(1, parseInt(e.target.value) || 1))}
            className="w-16 px-2 py-1 text-xs border rounded"
          />
          <button
            onClick={() => saveEditingQuantity(item.id)}
            disabled={isUpdating}
            className={`text-xs bg-green-100 hover:bg-green-200 text-green-800 px-2 py-1 rounded ${
              isUpdating ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {t('common.save')}
          </button>
          <button
            onClick={cancelEditingQuantity}
            className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded"
          >
            {t('common.cancel')}
          </button>
        </div>
      );
    }

    // 根据菜品状态显示不同的操作按钮
    switch (item.status) {
      case 'pending':
        return (
          <>
            <button
              onClick={() => handleItemStatusUpdate(item.id, 'in_progress')}
              disabled={isUpdating}
              className={`w-full text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded ${
                isUpdating ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {t('kitchen.startCooking')}
            </button>
            <div className="flex space-x-2 w-full">
              <button
                onClick={() => startEditingQuantity(item.id)}
                disabled={isUpdating}
                className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded"
                title={t('common.edit')}
              >
                {t('common.edit')}
              </button>
              <button
                onClick={() => handleCancelItem(item.id)}
                disabled={isUpdating}
                className="flex-1 text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded"
                title={t('common.cancel')}
              >
                {t('common.cancel')}
              </button>
            </div>
          </>
        );
      case 'in_progress':
        return (
          <>
            <button
              onClick={() => handleItemStatusUpdate(item.id, 'ready')}
              disabled={isUpdating}
              className={`w-full text-xs bg-green-100 hover:bg-green-200 text-green-800 px-2 py-1 rounded ${
                isUpdating ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {t('kitchen.markAsReady')}
            </button>
            <div className="flex space-x-2 w-full">
              <button
                onClick={() => startEditingQuantity(item.id)}
                disabled={isUpdating}
                className="flex-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-2 py-1 rounded"
                title={t('common.edit')}
              >
                {t('common.edit')}
              </button>
              <button
                onClick={() => handleCancelItem(item.id)}
                disabled={isUpdating}
                className="flex-1 text-xs bg-red-100 hover:bg-red-200 text-red-800 px-2 py-1 rounded"
                title={t('common.cancel')}
              >
                {t('common.cancel')}
              </button>
            </div>
          </>
        );
      case 'ready':
        return (
          <button
            onClick={() => handleItemStatusUpdate(item.id, 'served')}
            disabled={isUpdating}
            className={`w-full text-xs bg-purple-100 hover:bg-purple-200 text-purple-800 px-2 py-1 rounded ${
              isUpdating ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {t('kitchen.markAsCompleted')}
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
        <div>
          <span className="font-semibold">{t('common.table')} {order.tables.table_number}</span>
          <span className="ml-2 text-sm text-gray-500">
            {formatDistanceToNow(new Date(order.created_at), { addSuffix: true })}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={printOrder}
            className="text-gray-500 hover:text-gray-700"
            title={t('kitchen.printOrder')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
          </button>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
            {t(`status.${getStatusTranslationKey(order.status)}`)}
          </span>
        </div>
      </div>

      <div className="p-4">
        <h3 className="font-medium mb-2">{t('kitchen.items')}</h3>
        <ul className="divide-y">
          {order.items.map(item => (
            <li key={item.id} className="py-3">
              <div className="grid grid-cols-12 gap-2">
                {/* 左侧：菜品信息 */}
                <div className="col-span-8">
                  {/* 菜品名称 - 中文和日文 */}
                  <div className="mb-1">
                    <div className="font-medium text-base">
                      <span className="mr-2">{item.quantity}x</span>
                      {item.dishes?.name_zh || item.dish_name}
                    </div>
                    <div className="text-gray-500 text-sm">
                      {item.dishes?.name_ja || ''}
                    </div>
                  </div>

                  {/* 状态和价格信息 */}
                  <div className="flex items-center justify-between mt-2">
                    <span className={`px-2 py-0.5 rounded-full text-xs ${getStatusBadgeColor(item.status)}`}>
                      {t(`status.${getStatusTranslationKey(item.status)}`)}
                    </span>
                    <div className="text-sm">
                      <span className="text-gray-600">¥{item.price.toLocaleString()} × {item.quantity} = </span>
                      <span className="font-medium">¥{(item.price * item.quantity).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* 右侧：操作按钮 */}
                <div className="col-span-4 flex flex-col justify-center items-end space-y-2">
                  {getItemActionButton(item)}
                </div>
              </div>
            </li>
          ))}
        </ul>

        {/* 显示订单总价 */}
        <div className="mt-4 border-t pt-4 flex justify-between items-center">
          <div className="font-medium text-lg">
            {t('common.total')}: ¥{order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0).toLocaleString()}
          </div>
          <div>
            {getOrderActionButton()}
          </div>
        </div>
      </div>
    </div>
  );
}
