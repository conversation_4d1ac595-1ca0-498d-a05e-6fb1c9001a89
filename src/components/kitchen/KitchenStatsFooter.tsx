'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

type DishStat = {
  name: string;
  count: number;
  revenue: number;
};

type KitchenStats = {
  totalOrders: number;
  totalAmount: number;
  topDishes: DishStat[];
};

export default function KitchenStatsFooter() {
  const t = useTranslations();
  const [stats, setStats] = useState<KitchenStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 获取统计数据
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/kitchen/stats');
        
        if (!response.ok) {
          throw new Error(`Error fetching stats: ${response.statusText}`);
        }
        
        const data = await response.json();
        setStats(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching kitchen stats:', err);
        setError(t('kitchen.errorFetchingStats'));
      } finally {
        setLoading(false);
      }
    };

    fetchStats();

    // 每5分钟刷新一次统计数据
    const interval = setInterval(fetchStats, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [t]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', { style: 'currency', currency: 'JPY' }).format(amount);
  };

  if (loading) {
    return (
      <div className="bg-white border-t shadow-sm py-3 px-4">
        <div className="max-w-7xl mx-auto flex justify-center">
          <div className="animate-pulse flex space-x-4 w-full">
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white border-t shadow-sm py-3 px-4">
        <div className="max-w-7xl mx-auto">
          <p className="text-red-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="bg-white border-t shadow-sm py-3 px-4">
        <div className="max-w-7xl mx-auto">
          <p className="text-gray-500 text-sm">{t('kitchen.noStatsAvailable')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border-t shadow-sm py-3 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-wrap justify-between items-center">
          {/* 今日订单总数 */}
          <div className="px-4 py-2">
            <h3 className="text-xs font-medium text-gray-500">{t('kitchen.todayOrders')}</h3>
            <p className="text-lg font-semibold">{stats.totalOrders}</p>
          </div>
          
          {/* 今日销售总额 */}
          <div className="px-4 py-2">
            <h3 className="text-xs font-medium text-gray-500">{t('kitchen.todayRevenue')}</h3>
            <p className="text-lg font-semibold">{formatCurrency(stats.totalAmount)}</p>
          </div>
          
          {/* 热门菜品 */}
          <div className="px-4 py-2 flex-grow">
            <h3 className="text-xs font-medium text-gray-500">{t('kitchen.topDishes')}</h3>
            <div className="flex flex-wrap gap-x-4">
              {stats.topDishes.length === 0 ? (
                <p className="text-sm text-gray-500">{t('kitchen.noData')}</p>
              ) : (
                stats.topDishes.slice(0, 3).map((dish, index) => (
                  <div key={index} className="flex items-center">
                    <span className="text-sm font-medium">{index + 1}. {dish.name}</span>
                    <span className="ml-1 text-xs text-gray-500">({dish.count})</span>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
