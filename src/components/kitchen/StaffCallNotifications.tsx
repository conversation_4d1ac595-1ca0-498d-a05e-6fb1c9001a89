'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

interface StaffCall {
  id: string;
  tableId: string;
  tableNumber: string;
  timestamp: string;
  count: number;
}

interface StaffCallNotificationsProps {
  onNewCall?: (call: StaffCall) => void;
}

export default function StaffCallNotifications({ onNewCall }: StaffCallNotificationsProps) {
  const t = useTranslations();
  const [calls, setCalls] = useState<StaffCall[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  // 从localStorage加载消息
  useEffect(() => {
    const savedCalls = localStorage.getItem('staff_calls');
    if (savedCalls) {
      try {
        setCalls(JSON.parse(savedCalls));
      } catch (error) {
        console.error('Error parsing saved staff calls:', error);
      }
    }
  }, []);

  // 保存消息到localStorage
  const saveCalls = (newCalls: StaffCall[]) => {
    localStorage.setItem('staff_calls', JSON.stringify(newCalls));
    setCalls(newCalls);
  };

  // 添加新的呼叫
  const addCall = (tableId: string, tableNumber: string) => {
    const existingCallIndex = calls.findIndex(call => call.tableId === tableId);
    
    if (existingCallIndex !== -1) {
      // 如果已存在，增加计数
      const updatedCalls = [...calls];
      updatedCalls[existingCallIndex] = {
        ...updatedCalls[existingCallIndex],
        count: updatedCalls[existingCallIndex].count + 1,
        timestamp: new Date().toISOString()
      };
      saveCalls(updatedCalls);
      
      if (onNewCall) {
        onNewCall(updatedCalls[existingCallIndex]);
      }
    } else {
      // 创建新的呼叫记录
      const newCall: StaffCall = {
        id: `${tableId}_${Date.now()}`,
        tableId,
        tableNumber,
        timestamp: new Date().toISOString(),
        count: 1
      };
      
      const updatedCalls = [newCall, ...calls];
      saveCalls(updatedCalls);
      
      if (onNewCall) {
        onNewCall(newCall);
      }
    }
  };

  // 删除单个呼叫
  const removeCall = (callId: string) => {
    const updatedCalls = calls.filter(call => call.id !== callId);
    saveCalls(updatedCalls);
  };

  // 清空所有呼叫
  const clearAllCalls = () => {
    saveCalls([]);
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // 暴露addCall方法给父组件
  useEffect(() => {
    // 将addCall方法挂载到window对象，供SSE事件调用
    (window as any).addStaffCall = addCall;
    
    return () => {
      delete (window as any).addStaffCall;
    };
  }, [calls]);

  const unreadCount = calls.length;

  return (
    <div className="relative">
      {/* 消息按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          relative p-2 rounded-lg transition-colors duration-200
          ${unreadCount > 0 
            ? 'bg-red-500 text-white hover:bg-red-600' 
            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          }
        `}
      >
        {/* 铃铛图标 */}
        <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-7A2.5 2.5 0 016.5 7h11A2.5 2.5 0 0120 9.5v7a2.5 2.5 0 01-2.5 2.5H13" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a3 3 0 11-6 0c0-1.5.5-3 3-3s3 1.5 3 3z" />
        </svg>
        
        {/* 未读消息数量徽章 */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* 消息列表弹窗 */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-800">{t('kitchen.staffCalls')}</h3>
              {calls.length > 0 && (
                <button
                  onClick={clearAllCalls}
                  className="text-sm text-red-600 hover:text-red-700"
                >
                  {t('kitchen.clearAll')}
                </button>
              )}
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {calls.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {t('kitchen.noStaffCalls')}
              </div>
            ) : (
              calls.map(call => (
                <div key={call.id} className="p-4 border-b hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-800">
                          {t('kitchen.table')} {call.tableNumber}
                        </span>
                        {call.count > 1 && (
                          <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                            {call.count}x
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-500 mt-1">
                        {formatTime(call.timestamp)}
                      </div>
                    </div>
                    <button
                      onClick={() => removeCall(call.id)}
                      className="text-gray-400 hover:text-red-500 ml-2"
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* 点击外部关闭弹窗 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
