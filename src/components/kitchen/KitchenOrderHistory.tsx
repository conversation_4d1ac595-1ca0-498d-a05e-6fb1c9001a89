'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { format, subDays } from 'date-fns';

type OrderItem = {
  id: string;
  dish_id: string;
  dish_name: string;
  quantity: number;
  price: number;
  status: string;
};

type Order = {
  id: string;
  table_id: string;
  table_number: string;
  status: string;
  created_at: string;
  items: OrderItem[];
};

type OrderStats = {
  totalOrders: number;
  totalAmount: number;
  topDishes: {
    name: string;
    count: number;
    revenue: number;
  }[];
};

type KitchenOrderHistoryProps = {
  onClose: () => void;
};

export default function KitchenOrderHistory({ onClose }: KitchenOrderHistoryProps) {
  const t = useTranslations();
  const [orders, setOrders] = useState<Order[]>([]);
  const [stats, setStats] = useState<OrderStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);

  useEffect(() => {
    fetchOrderHistory(selectedDate);
  }, [selectedDate]);

  const fetchOrderHistory = async (date: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/kitchen/orders/history?date=${date}`);
      
      if (!response.ok) {
        throw new Error(`Error fetching order history: ${response.statusText}`);
      }
      
      const data = await response.json();
      setOrders(data.orders || []);
      setStats(data.stats || null);
    } catch (error: any) {
      console.error('Error fetching order history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (daysAgo: number) => {
    const date = subDays(new Date(), daysAgo);
    setSelectedDate(format(date, 'yyyy-MM-dd'));
  };

  const toggleOrderExpand = (orderId: string) => {
    if (expandedOrder === orderId) {
      setExpandedOrder(null);
    } else {
      setExpandedOrder(orderId);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP', { style: 'currency', currency: 'JPY' }).format(amount);
  };

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'HH:mm');
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="px-6 py-4 border-b flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">{t('kitchen.orderHistory')}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="px-6 py-4 border-b">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedDate(format(new Date(), 'yyyy-MM-dd'))}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedDate === format(new Date(), 'yyyy-MM-dd')
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {t('kitchen.today')}
            </button>
            <button
              onClick={() => handleDateChange(1)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedDate === format(subDays(new Date(), 1), 'yyyy-MM-dd')
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {t('kitchen.yesterday')}
            </button>
            <button
              onClick={() => handleDateChange(2)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedDate === format(subDays(new Date(), 2), 'yyyy-MM-dd')
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {t('kitchen.twoDaysAgo')}
            </button>
            <button
              onClick={() => handleDateChange(3)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                selectedDate === format(subDays(new Date(), 3), 'yyyy-MM-dd')
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {t('kitchen.threeDaysAgo')}
            </button>
            <div className="ml-auto">
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="px-4 py-2 border rounded-md text-sm"
              />
            </div>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* 订单列表 */}
          <div className="w-2/3 overflow-y-auto p-6">
            <h3 className="text-lg font-medium mb-4">{t('kitchen.completedOrders')}</h3>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : orders.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                {t('kitchen.noOrdersFound')}
              </div>
            ) : (
              <div className="space-y-4">
                {orders.map((order) => (
                  <div
                    key={order.id}
                    className="bg-white border rounded-lg shadow-sm overflow-hidden"
                  >
                    <div
                      className="px-4 py-3 bg-gray-50 flex justify-between items-center cursor-pointer"
                      onClick={() => toggleOrderExpand(order.id)}
                    >
                      <div className="flex items-center">
                        <span className="font-medium">{t('common.table')} {order.table_number}</span>
                        <span className="ml-4 text-sm text-gray-500">
                          {format(new Date(order.created_at), 'yyyy-MM-dd HH:mm')}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="px-2 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-800">
                          {t('status.completed')}
                        </span>
                        <svg
                          className={`ml-2 h-5 w-5 text-gray-500 transform transition-transform ${
                            expandedOrder === order.id ? 'rotate-180' : ''
                          }`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                    
                    {expandedOrder === order.id && (
                      <div className="px-4 py-3 border-t">
                        <h4 className="font-medium mb-2">{t('kitchen.items')}</h4>
                        <ul className="divide-y">
                          {order.order_items && order.order_items.map((item) => (
                            <li key={item.id} className="py-2 flex justify-between">
                              <div>
                                <span className="font-medium">{item.quantity}x</span> {item.dish_name}
                              </div>
                              <div className="text-gray-700">
                                {formatCurrency(item.price * item.quantity)}
                              </div>
                            </li>
                          ))}
                        </ul>
                        <div className="mt-3 pt-3 border-t flex justify-end">
                          <div className="font-medium">
                            {t('common.total')}: {formatCurrency(
                              order.order_items
                                ? order.order_items.reduce((sum, item) => sum + item.price * item.quantity, 0)
                                : 0
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* 统计信息 */}
          <div className="w-1/3 bg-gray-50 p-6 overflow-y-auto border-l">
            <h3 className="text-lg font-medium mb-4">{t('kitchen.statistics')}</h3>
            
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : !stats ? (
              <div className="text-center py-12 text-gray-500">
                {t('kitchen.noStatsAvailable')}
              </div>
            ) : (
              <div className="space-y-6">
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="text-sm font-medium text-gray-500 mb-1">{t('kitchen.totalOrders')}</h4>
                  <p className="text-2xl font-bold">{stats.totalOrders}</p>
                </div>
                
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="text-sm font-medium text-gray-500 mb-1">{t('kitchen.totalRevenue')}</h4>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</p>
                </div>
                
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">{t('kitchen.topSellingDishes')}</h4>
                  {stats.topDishes.length === 0 ? (
                    <p className="text-gray-500 text-sm">{t('kitchen.noData')}</p>
                  ) : (
                    <ul className="space-y-2">
                      {stats.topDishes.slice(0, 5).map((dish, index) => (
                        <li key={index} className="flex justify-between items-center">
                          <div className="flex items-center">
                            <span className="w-5 text-center font-medium text-gray-500">{index + 1}.</span>
                            <span className="ml-2 truncate">{dish.name}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            {dish.count} {t('kitchen.units')} ({formatCurrency(dish.revenue)})
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
