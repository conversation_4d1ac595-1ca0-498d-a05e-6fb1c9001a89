'use client';

import { useTranslations } from 'next-intl';
import { formatDistanceToNow } from 'date-fns';

type OrderItem = {
  id: string;
  dish_id: string;
  dish_name: string;
  quantity: number;
  price: number;
  status: string;
};

type Order = {
  id: string;
  table_id: string;
  table_number: string;
  status: string;
  created_at: string;
  items: OrderItem[];
};

type KitchenOrderCardProps = {
  order: Order;
  onUpdateOrderStatus: (orderId: string, status: string) => void;
  onUpdateItemStatus: (itemId: string, status: string) => void;
};

export default function KitchenOrderCard({
  order,
  onUpdateOrderStatus,
  onUpdateItemStatus
}: KitchenOrderCardProps) {
  const t = useTranslations();

  // 获取状态的翻译键
  const getStatusTranslationKey = (status: string): string => {
    switch (status) {
      case 'in_progress':
        return 'inProgress';
      case 'awaiting_payment':
        return 'awaitingPayment';
      default:
        return status.replace(/_/g, '');
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'served':
        return 'bg-purple-100 text-purple-800';
      case 'awaiting_payment':
        return 'bg-orange-100 text-orange-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderActionButton = () => {
    switch (order.status) {
      case 'pending':
        return (
          <button
            onClick={() => onUpdateOrderStatus(order.id, 'in_progress')}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            {t('kitchen.startCooking')}
          </button>
        );
      case 'in_progress':
        // Check if all items are ready or served
        const allItemsReadyOrServed = order.items.every(
          item => item.status === 'ready' || item.status === 'served'
        );

        if (allItemsReadyOrServed) {
          return (
            <button
              onClick={() => onUpdateOrderStatus(order.id, 'awaiting_payment')}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              {t('kitchen.markAsCompleted')}
            </button>
          );
        }
        return null;
      case 'awaiting_payment':
        return (
          <button
            onClick={() => onUpdateOrderStatus(order.id, 'completed')}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            {t('kitchen.markAsCompleted')}
          </button>
        );
      default:
        return null;
    }
  };

  const getItemActionButton = (item: OrderItem) => {
    switch (item.status) {
      case 'pending':
        return (
          <button
            onClick={() => onUpdateItemStatus(item.id, 'in_progress')}
            className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 px-2 py-1 rounded"
          >
            {t('kitchen.startCooking')}
          </button>
        );
      case 'in_progress':
        return (
          <button
            onClick={() => onUpdateItemStatus(item.id, 'ready')}
            className="text-xs bg-green-100 hover:bg-green-200 text-green-800 px-2 py-1 rounded"
          >
            {t('kitchen.markAsReady')}
          </button>
        );
      case 'ready':
        return (
          <button
            onClick={() => onUpdateItemStatus(item.id, 'served')}
            className="text-xs bg-purple-100 hover:bg-purple-200 text-purple-800 px-2 py-1 rounded"
          >
            {t('kitchen.markAsCompleted')}
          </button>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
        <div>
          <span className="font-semibold">{t('common.table')} {order.table_number}</span>
          <span className="ml-2 text-sm text-gray-500">
            {formatDistanceToNow(new Date(order.created_at), { addSuffix: true })}
          </span>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(order.status)}`}>
          {t(`status.${getStatusTranslationKey(order.status)}`)}
        </span>
      </div>

      <div className="p-4">
        <h3 className="font-medium mb-2">{t('kitchen.items')}</h3>
        <ul className="divide-y">
          {order.items.map(item => (
            <li key={item.id} className="py-2 flex justify-between items-center">
              <div>
                <span className="font-medium">{item.quantity}x</span> {item.dish_name}
                <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getStatusBadgeColor(item.status)}`}>
                  {t(`status.${getStatusTranslationKey(item.status)}`)}
                </span>
              </div>
              {getItemActionButton(item)}
            </li>
          ))}
        </ul>

        <div className="mt-4 flex justify-end">
          {getOrderActionButton()}
        </div>
      </div>
    </div>
  );
}
