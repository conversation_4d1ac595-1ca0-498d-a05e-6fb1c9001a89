
export const locales = ['en', 'ja', 'ko', 'zh'] as const;
export type Locale = (typeof locales)[number];

export const pathnames = {
  '/': '/',
  '/customer/[tableId]': '/customer/[tableId]',
  '/kitchen': '/kitchen',
  '/admin': '/admin',
} as const;

export const localePrefix = 'always'; // 'as-needed' | 'always' | 'never'

export default {
  defaultLocale: 'en' as Locale,
  locales,
  localePrefix,
  pathnames
};
