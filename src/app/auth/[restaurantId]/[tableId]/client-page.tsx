'use client';

import { useEffect, useState } from 'react';
import Loading from '@/components/common/Loading';
import { apiGet } from '@/lib/api';
import { useCartStore } from '@/lib/stores/cartStore';

export default function AuthClientPage({
  restaurantId,
  tableId
}: {
  restaurantId: string;
  tableId: string;
}) {
  const [error, setError] = useState<string | null>(null);
  const clearAllCart = useCartStore(state => state.clearAllCart);

  // 检测用户的浏览器语言
  const detectUserLanguage = () => {
    try {
      // 首先检查localStorage中是否有保存的语言偏好
      const savedLanguage = localStorage.getItem('preferred_language');
      if (savedLanguage) {
        return savedLanguage;
      }

      // 如果没有保存的偏好，则检测浏览器语言
      if (typeof window !== 'undefined') {
        const browserLang = navigator.language || (navigator as any).userLanguage;
        const langCode = browserLang.split('-')[0];

        // 检查是否支持该语言
        const supportedLanguages = ['en', 'ja', 'ko', 'zh'];

        if (supportedLanguages.includes(langCode)) {
          return langCode;
        }
      }
    } catch (error: any) {
      console.error('Error detecting language:', error);
    }

    // 默认返回英语
    return 'en';
  };

  // 获取餐桌编号
  useEffect(() => {
    // 确保代码只在客户端执行
    if (typeof window === 'undefined') return;

    const fetchTableNumberAndGenerateToken = async () => {
      try {
        // 获取餐桌编号
        const response = await fetch(`/api/tables?id=${tableId}`);
        const { data } = await response.json();
        console.log('Table data:', data);

        if (!response.ok) {
          console.error('Error fetching table number');
        } else if (data) {
          // 将餐桌编号存储在localStorage中
          localStorage.setItem('table_number', data.table_number);
          // localStorage.setItem('table_id', tableId);
        }
      } catch (error: any) {
        console.error('Error fetching table number:', error);
      }

      // 生成令牌
      try {
        // 获取用户语言
        const userLang = detectUserLanguage();

        // Call the API to generate a token
        const response = await fetch(`/api/auth/generate-token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            restaurantId,
            tableId,
          }),
        });

        let data;
        try {
          data = await response.json();
        } catch (jsonError) {
          throw new Error('Invalid response from server');
        }

        if (!response.ok) {
          throw new Error(data.error || 'Failed to generate token');
        }

        // Store the token, restaurant ID and order ID in localStorage
        try {
          
          if(data.orderIsNew) {
                      console.log('Clearing all cart items');

            clearAllCart();
          }
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('restaurant_id', restaurantId);
          localStorage.setItem('table_id', tableId);
          localStorage.setItem('token_expiry', data.expiry);
          localStorage.setItem('order_id', data.orderId); // 存储订单ID
        } catch (storageError) {
          console.error('Error storing data in localStorage:', storageError);
        }

        // Redirect to the ordering page and replace the history entry
        // This prevents the user from going back to this page
        // todo: guestcount
        window.location.replace(`/${userLang}/customer/${tableId}?skipChooseGuestCount=${!data.orderIsNew}`);
      } catch (error: any) {
        console.error('Error generating token:', error);
        setError(error instanceof Error ? error.message : 'Failed to authenticate. Please try again.');
      }
    };

    fetchTableNumberAndGenerateToken();
  }, [restaurantId, tableId]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
          <p className="text-gray-700">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Prepare...</h1>
        <p className="text-gray-700 mb-8">Please wait while we prepare your table.</p>
        <Loading />
      </div>
    </div>
  );
}
