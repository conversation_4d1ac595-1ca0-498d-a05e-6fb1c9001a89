'use client';

import { useEffect } from 'react';

export default function Home() {
  useEffect(() => {
    // 检测用户的浏览器语言或已保存的偏好
    const detectUserLanguage = () => {
      // 首先检查localStorage中是否有保存的语言偏好
      const savedLanguage = localStorage.getItem('preferred_language');
      if (savedLanguage) {
        return savedLanguage;
      }

      // 如果没有保存的偏好，则检测浏览器语言
      const browserLang = navigator.language || (navigator as any).userLanguage;
      const langCode = browserLang.split('-')[0];

      // 检查是否支持该语言
      const supportedLanguages = ['en', 'ja', 'ko', 'zh'];

      if (supportedLanguages.includes(langCode)) {
        return langCode;
      }

      // 默认返回英语
      return 'en';
    };

    const userLang = detectUserLanguage();
    window.location.href = `/${userLang}`;
  }, []);

  // 显示加载中，直到重定向完成
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Loading...</h1>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
      </div>
    </div>
  );
}
