import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * 添加订单项
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { orderItems } = body;

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return NextResponse.json({ error: 'Order items are required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 添加订单项
    const { data, error } = await supabase
      .from('order_items')
      .insert(orderItems)
      .select();

    if (error) {
      console.error('Error adding order items:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    console.error('Error in order items API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
