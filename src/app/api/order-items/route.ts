import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';
import { broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

/**
 * 添加订单项
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { orderItems } = body;

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return NextResponse.json({ error: 'Order items are required' }, { status: 400 });
    }

    // 创建 Supabase 客户端 (使用anon角色进行插入)
    const supabaseUrl = process.env.NEXT_SUPABASE_URL || process.env.SUPABASE_URL!;
    const supabaseAnonKey = process.env.NEXT_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY!;
    const anonSupabase = createSupabaseClient(supabaseUrl, supabaseAnonKey);

    // 添加订单项
    const { data, error } = await anonSupabase
      .from('order_items')
      .insert(orderItems)
      .select();

    if (error) {
      console.error('Error adding order items:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // 获取订单信息以便通知厨房
    if (data && data.length > 0) {
      try {
        const orderId = data[0].order_id;

        // 使用服务端客户端获取完整的订单信息
        const supabase = await createClient();
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            tables(table_number),
            order_items(
              *,
              dishes(name_zh, name_ja, name_en, name_ko)
            )
          `)
          .eq('id', orderId)
          .single();

        if (!orderError && orderData) {
          // 通过SSE通知厨房有新订单
          broadcastToRestaurant(orderData.restaurant_id, 'order_created', {
            order: orderData,
            timestamp: new Date().toISOString()
          });

          console.log(`SSE notification sent for new order: ${orderId} to restaurant: ${orderData.restaurant_id}`);
        }
      } catch (sseError) {
        console.error('Error sending SSE notification:', sseError);
        // 不要因为SSE通知失败而影响订单创建
      }
    }

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    console.error('Error in order items API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
