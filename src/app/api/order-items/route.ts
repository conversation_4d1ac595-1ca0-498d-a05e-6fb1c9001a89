import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import { broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

/**
 * 添加订单项
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { orderItems } = body;

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return NextResponse.json({ error: 'Order items are required' }, { status: 400 });
    }

    // 验证订单项的有效性
    const firstOrderId = orderItems[0].order_id;

    // 使用服务端客户端验证订单是否存在且有效
    const supabase = createServiceClient();
    const { data: orderCheck, error: orderCheckError } = await supabase
      .from('orders')
      .select('id, status, restaurant_id')
      .eq('id', firstOrderId)
      .single();

    if (orderCheckError || !orderCheck) {
      return NextResponse.json({ error: 'Invalid order ID' }, { status: 400 });
    }

    if (orderCheck.status === 'completed') {
      return NextResponse.json({ error: 'Cannot add items to completed order' }, { status: 400 });
    }

    // 验证所有订单项都属于同一个订单
    const allSameOrder = orderItems.every(item => item.order_id === firstOrderId);
    if (!allSameOrder) {
      return NextResponse.json({ error: 'All order items must belong to the same order' }, { status: 400 });
    }

    // 验证菜品是否存在且属于正确的餐厅
    const dishIds = orderItems.map(item => item.dish_id);
    const { data: dishCheck, error: dishCheckError } = await supabase
      .from('dishes')
      .select('id, restaurant_id')
      .in('id', dishIds);

    if (dishCheckError || !dishCheck || dishCheck.length !== dishIds.length) {
      return NextResponse.json({ error: 'Invalid dish IDs' }, { status: 400 });
    }

    // 验证所有菜品都属于同一个餐厅
    const allSameRestaurant = dishCheck.every(dish => dish.restaurant_id === orderCheck.restaurant_id);
    if (!allSameRestaurant) {
      return NextResponse.json({ error: 'All dishes must belong to the same restaurant as the order' }, { status: 400 });
    }

    // 使用服务端客户端插入订单项（绕过RLS）
    const { data, error } = await supabase
      .from('order_items')
      .insert(orderItems)
      .select();

    if (error) {
      console.error('Error adding order items:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // 获取订单信息以便通知厨房
    if (data && data.length > 0) {
      try {
        const orderId = data[0].order_id;

        // 获取完整的订单信息
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            tables(table_number),
            order_items(
              *,
              dishes(name_zh, name_ja, name_en, name_ko)
            )
          `)
          .eq('id', orderId)
          .single();

        if (!orderError && orderData) {
          // 通过SSE通知厨房有新订单
          broadcastToRestaurant(orderData.restaurant_id, 'order_created', {
            order: orderData,
            timestamp: new Date().toISOString()
          });

          console.log(`SSE notification sent for new order: ${orderId} to restaurant: ${orderData.restaurant_id}`);
        }
      } catch (sseError) {
        console.error('Error sending SSE notification:', sseError);
        // 不要因为SSE通知失败而影响订单创建
      }
    }

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    console.error('Error in order items API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
