import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import { broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

/**
 * 添加订单项
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { orderItems, token, guestCount } = body;

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return NextResponse.json({ error: 'Order items are required' }, { status: 400 });
    }

    if (!token) {
      return NextResponse.json({ error: 'Authentication token is required' }, { status: 401 });
    }

    // 验证token和订单有效性
    const firstOrderId = orderItems[0].order_id;

    console.log('🔐 Server token validation:', {
      hasToken: !!token,
      tokenLength: token?.length,
      firstOrderId,
      orderItemsCount: orderItems.length,
      guestCount
    });

    // 使用服务端客户端进行验证
    const supabase = createServiceClient();

    // 首先验证token是否有效
    const { data: tokenData, error: tokenError } = await supabase
      .from('auth_tokens')
      .select('order_id, restaurant_id, table_id, expires_at')
      .eq('token', token)
      .single();

    console.log('🔍 Token query result:', {
      found: !!tokenData,
      error: tokenError?.message,
      tokenOrderId: tokenData?.order_id,
      requestOrderId: firstOrderId,
      expiresAt: tokenData?.expires_at
    });

    if (tokenError || !tokenData) {
      console.error('Token validation error:', tokenError);
      return NextResponse.json({ error: 'Invalid or expired session' }, { status: 401 });
    }

    // 检查token是否过期
    const expiryTime = new Date(tokenData.expires_at);
    const currentTime = new Date();
    if (currentTime > expiryTime) {
      console.error('Token expired:', { expiryTime, currentTime });
      return NextResponse.json({ error: 'Session has expired' }, { status: 401 });
    }

    // 验证token关联的订单ID与请求中的订单ID是否匹配
    if (tokenData.order_id !== firstOrderId) {
      console.error('Order ID mismatch:', { tokenOrderId: tokenData.order_id, requestOrderId: firstOrderId });
      return NextResponse.json({ error: 'Order ID mismatch' }, { status: 401 });
    }

    // 验证订单是否存在且状态
    const { data: orderCheck, error: orderCheckError } = await supabase
      .from('orders')
      .select('id, status, restaurant_id, table_id')
      .eq('id', firstOrderId)
      .single();

    if (orderCheckError || !orderCheck) {
      return NextResponse.json({ error: 'Invalid order ID' }, { status: 400 });
    }

    // 添加订单状态检查的调试信息
    console.log('📋 Order status check:', {
      orderId: firstOrderId,
      orderStatus: orderCheck.status,
      needsNewOrder: orderCheck.status === 'completed' || orderCheck.status === 'cancelled'
    });

    let actualOrderId = firstOrderId;

    // 如果订单已完成或已取消，创建新订单
    if (orderCheck.status === 'completed' || orderCheck.status === 'cancelled') {
      console.log(`📋 Order ${firstOrderId} is ${orderCheck.status}, creating new order`);

      // 创建新订单
      const { data: newOrder, error: createOrderError } = await supabase
        .from('orders')
        .insert({
          table_id: orderCheck.table_id,
          restaurant_id: orderCheck.restaurant_id,
          status: 'pending',
          guest_count: guestCount || 1, // 使用前端传递的客人数量
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createOrderError || !newOrder) {
        console.error('Error creating new order:', createOrderError);
        return NextResponse.json({ error: 'Failed to create new order' }, { status: 500 });
      }

      actualOrderId = newOrder.id;
      console.log(`✅ Created new order: ${actualOrderId}`);

      // 更新token关联的订单ID
      const { error: updateTokenError } = await supabase
        .from('auth_tokens')
        .update({ order_id: actualOrderId })
        .eq('token', token);

      if (updateTokenError) {
        console.error('Error updating token order_id:', updateTokenError);
        // 不返回错误，因为订单已创建成功
      }

      // 更新订单项的order_id
      orderItems.forEach(item => {
        item.order_id = actualOrderId;
      });
    }

    // 验证所有订单项都属于同一个订单（使用实际的订单ID）
    const allSameOrder = orderItems.every(item => item.order_id === actualOrderId);
    if (!allSameOrder) {
      return NextResponse.json({ error: 'All order items must belong to the same order' }, { status: 400 });
    }

    // 验证菜品是否存在且属于正确的餐厅
    const dishIds = orderItems.map(item => item.dish_id);
    const { data: dishCheck, error: dishCheckError } = await supabase
      .from('dishes')
      .select('id, restaurant_id')
      .in('id', dishIds);

    if (dishCheckError || !dishCheck || dishCheck.length !== dishIds.length) {
      return NextResponse.json({ error: 'Invalid dish IDs' }, { status: 400 });
    }

    // 验证所有菜品都属于同一个餐厅
    const allSameRestaurant = dishCheck.every(dish => dish.restaurant_id === orderCheck.restaurant_id);
    if (!allSameRestaurant) {
      return NextResponse.json({ error: 'All dishes must belong to the same restaurant as the order' }, { status: 400 });
    }

    // 使用服务端客户端插入订单项（绕过RLS）
    const { data, error } = await supabase
      .from('order_items')
      .insert(orderItems)
      .select();

    if (error) {
      console.error('Error adding order items:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // 获取订单信息以便通知厨房
    if (data && data.length > 0) {
      try {
        const orderId = data[0].order_id;

        // 获取完整的订单信息
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            tables(table_number),
            order_items(
              *,
              dishes(name_zh, name_ja, name_en, name_ko)
            )
          `)
          .eq('id', orderId)
          .single();

        if (!orderError && orderData) {
          // 处理订单数据以匹配前端期望的格式
          const processedOrderItems = orderData.order_items?.map((item: any) => {
            const dishName = item.dishes ? (
              item.dishes.name_en ||
              item.dishes.name_ja ||
              item.dishes.name_zh ||
              item.dishes.name_ko ||
              'Unknown Dish'
            ) : 'Unknown Dish';

            return {
              ...item,
              dish_name: dishName
            };
          }) || [];

          const processedOrder = {
            ...orderData,
            items: processedOrderItems,
            order_items: processedOrderItems // 保持兼容性
          };

          // 通过SSE通知厨房有新订单
          broadcastToRestaurant(orderData.restaurant_id, 'order_created', {
            order: processedOrder,
            timestamp: new Date().toISOString()
          });

          console.log(`SSE notification sent for new order: ${orderId} to restaurant: ${orderData.restaurant_id}`);
        }
      } catch (sseError) {
        console.error('Error sending SSE notification:', sseError);
        // 不要因为SSE通知失败而影响订单创建
      }
    }

    return NextResponse.json({
      success: true,
      data,
      orderId: actualOrderId, // 返回实际使用的订单ID
      newOrderCreated: actualOrderId !== firstOrderId // 是否创建了新订单
    });
  } catch (error: any) {
    console.error('Error in order items API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
