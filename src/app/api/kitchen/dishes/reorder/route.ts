import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

// 更新菜品排序
export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { dishes, restaurantId } = body;

    if (!dishes || !Array.isArray(dishes) || !restaurantId) {
      return NextResponse.json(
        { error: 'Invalid request data. Dishes array and restaurantId are required.' },
        { status: 400 }
      );
    }

    // 获取用户信息
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // 逐个更新菜品排序，而不是使用批量操作
    let updateError = null;
    
    for (const update of dishes) {
      const { error } = await supabase
        .from('dishes')
        .update({ sort_order: update.sort_order })
        .eq('id', update.id)
        .eq('restaurant_id', restaurantId);
      
      if (error) {
        updateError = error;
        console.error('Error updating dish:', update.id, error);
        break;
      }
    }

    if (updateError) {
      console.error('Error updating dish order:', updateError);
      return NextResponse.json(
        { error: 'Failed to update dish order: ' + updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true
    });
  } catch (error: any) {
    console.error('Error in reorder dishes API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
