import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { translateDish } from '@/lib/translation';
import { requireKitchenAccess } from '@/lib/auth/permissions';

// 添加新菜品
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const permissions = await requireKitchenAccess(request);

    if (permissions instanceof NextResponse) {
      return permissions; // 返回错误响应
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 解析请求体
    const body = await request.json();
    const { name, description, price, categoryId, inputLanguage, sortOrder, imageUrl } = body;

    if (!name || !categoryId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 使用当前用户的餐厅ID，而不是前端传递的
    const restaurantId = permissions.restaurantId;

    if (!restaurantId) {
      return NextResponse.json(
        { error: 'User is not associated with any restaurant.' },
        { status: 403 }
      );
    }

    // 翻译菜品名称和描述（AI自动检测语言）
    const translations = await translateDish(name, description || '', inputLanguage);

    // 准备插入数据
    const dishData = {
      name_zh: translations.name.zh,
      name_en: translations.name.en,
      name_ja: translations.name.ja,
      name_ko: translations.name.ko,
      description_zh: translations.description.zh || '',
      description_en: translations.description.en || '',
      description_ja: translations.description.ja || '',
      description_ko: translations.description.ko || '',
      price: price || 0,
      category_id: categoryId,
      restaurant_id: restaurantId,
      sort_order: sortOrder || 0,
      image_url: imageUrl || null
    };

    // 插入数据
    const { data, error } = await supabase
      .from('dishes')
      .insert(dishData)
      .select()
      .single();

    if (error) {
      console.error('Error creating dish:', error);
      return NextResponse.json(
        { error: 'Failed to create dish: ' + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error: any) {
    console.error('Error in create dish API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
