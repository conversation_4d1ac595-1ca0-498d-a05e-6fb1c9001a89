import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { translateDish } from '@/lib/translation';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

// 添加新菜品
export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { name, description, price, categoryId, inputLanguage, sortOrder, restaurantId, imageUrl } = body;

    if (!name || !categoryId || !restaurantId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 翻译菜品名称和描述
    const translations = await translateDish(name, description || '', inputLanguage);

    // 准备插入数据
    const dishData = {
      name_zh: translations.name.zh,
      name_en: translations.name.en,
      name_ja: translations.name.ja,
      name_ko: translations.name.ko,
      description_zh: translations.description.zh || '',
      description_en: translations.description.en || '',
      description_ja: translations.description.ja || '',
      description_ko: translations.description.ko || '',
      price: price || 0,
      category_id: categoryId,
      restaurant_id: restaurantId,
      sort_order: sortOrder || 0,
      image_url: imageUrl || null
    };

    // 插入数据
    const { data, error } = await supabase
      .from('dishes')
      .insert(dishData)
      .select()
      .single();

    if (error) {
      console.error('Error creating dish:', error);
      return NextResponse.json(
        { error: 'Failed to create dish: ' + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error: any) {
    console.error('Error in create dish API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
