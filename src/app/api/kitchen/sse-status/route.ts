import { NextRequest, NextResponse } from 'next/server';
import { getConnectionStats, broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

/**
 * 获取SSE连接状态
 */
export async function GET(request: NextRequest) {
  try {
    const stats = getConnectionStats();

    console.log('📊 Current SSE connection stats:', stats);

    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('Error getting SSE stats:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: error.message
    }, { status: 500 });
  }
}

/**
 * 手动测试SSE广播
 */
export async function POST(request: NextRequest) {
  try {
    const { restaurantId, event = 'test_event', message = 'Test message' } = await request.json();

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    console.log(`🧪 Manual SSE test broadcast to restaurant: ${restaurantId}`);

    // 获取连接统计
    const stats = getConnectionStats();
    console.log('📊 Connection stats before broadcast:', stats);

    // 广播测试消息
    broadcastToRestaurant(restaurantId, event, {
      message,
      timestamp: new Date().toISOString(),
      test: true
    });

    return NextResponse.json({
      success: true,
      restaurantId,
      event,
      message,
      connectionStats: stats
    });
  } catch (error: any) {
    console.error('Error testing SSE:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: error.message
    }, { status: 500 });
  }
}
