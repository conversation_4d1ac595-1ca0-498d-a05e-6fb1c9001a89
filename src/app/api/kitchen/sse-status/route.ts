import { NextRequest, NextResponse } from 'next/server';
import { getConnectionStats } from '@/app/api/kitchen/sse/route';

/**
 * 获取SSE连接状态
 */
export async function GET(request: NextRequest) {
  try {
    const stats = getConnectionStats();
    
    console.log('📊 Current SSE connection stats:', stats);
    
    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('Error getting SSE stats:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      message: error.message 
    }, { status: 500 });
  }
}
