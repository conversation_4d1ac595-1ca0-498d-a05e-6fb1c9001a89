import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // 获取请求参数
    const { searchParams } = new URL(request.url);
    const tableId = searchParams.get('tableId');
    const restaurantId = searchParams.get('restaurantId');

    if (!tableId || !restaurantId) {
      return NextResponse.json(
        { error: 'Table ID and Restaurant ID are required' },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 查询该餐桌的进行中订单
    const { data, error } = await supabase
      .from('orders')
      .select('id')
      .eq('table_id', tableId)
      .eq('restaurant_id', restaurantId)
      .in('status', ['pending', 'in_progress'])
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error checking active orders:', error);
      return NextResponse.json(
        { error: 'Failed to check active orders' },
        { status: 500 }
      );
    }

    // 返回是否有进行中的订单
    return NextResponse.json({
      hasActiveOrder: data && data.length > 0,
      orderId: data && data.length > 0 ? data[0].id : null
    });
  } catch (error: any) {
    console.error('Error in active orders API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
