import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { itemId, quantity, action } = body;

    if (!itemId || (action !== 'update' && action !== 'cancel') || (action === 'update' && !quantity)) {
      return NextResponse.json(
        { error: 'Item ID and valid action are required. For update action, quantity is required.' },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取当前订单项目信息
    const { data: currentItem, error: getItemError } = await supabase
      .from('order_items')
      .select('*')
      .eq('id', itemId)
      .single();

    if (getItemError || !currentItem) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      );
    }

    // 检查项目状态，只有 pending 或 in_progress 状态的项目才能修改
    if (currentItem.status !== 'pending' && currentItem.status !== 'in_progress') {
      return NextResponse.json(
        { error: 'Cannot modify items that are already ready or served' },
        { status: 400 }
      );
    }

    if (action === 'cancel') {
      // 取消菜品（删除订单项目）
      const { error: deleteError } = await supabase
        .from('order_items')
        .delete()
        .eq('id', itemId);

      if (deleteError) {
        throw deleteError;
      }

      return NextResponse.json({
        success: true,
        message: 'Order item cancelled successfully',
      });
    } else if (action === 'update') {
      // 更新菜品数量
      if (quantity <= 0) {
        return NextResponse.json(
          { error: 'Quantity must be greater than 0' },
          { status: 400 }
        );
      }

      const { error: updateError } = await supabase
        .from('order_items')
        .update({ quantity })
        .eq('id', itemId);

      if (updateError) {
        throw updateError;
      }

      return NextResponse.json({
        success: true,
        message: 'Order item quantity updated successfully',
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error: any) {
    console.error('Error updating order item:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
