import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { startOfDay, endOfDay, subDays } from 'date-fns';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

export async function GET(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const restaurantId = searchParams.get('restaurantId');
    const dateParam = searchParams.get('date'); // 格式: YYYY-MM-DD
    const daysAgo = searchParams.get('daysAgo'); // 几天前的数据

    let startDate: Date;
    let endDate: Date;

    if (dateParam) {
      // 如果提供了具体日期，使用该日期
      startDate = startOfDay(new Date(dateParam));
      endDate = endOfDay(new Date(dateParam));
    } else if (daysAgo && !isNaN(Number(daysAgo))) {
      // 如果提供了daysAgo参数，获取指定天数前的数据
      startDate = startOfDay(subDays(new Date(), Number(daysAgo)));
      endDate = endOfDay(subDays(new Date(), Number(daysAgo)));
    } else {
      // 默认获取今天的数据
      startDate = startOfDay(new Date());
      endDate = endOfDay(new Date());
    }

    // 构建查询
    let query = supabase
      .from('orders')
      .select(`
        *,
        tables(table_number)
      `)
      .eq('status', 'completed') // 只获取已完成的订单
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    // 如果提供了餐厅 ID，则按餐厅筛选
    if (restaurantId) {
      query = query.eq('restaurant_id', restaurantId);
    }

    // 执行查询
    const { data: ordersData, error: ordersError } = await query;

    if (ordersError) {
      throw ordersError;
    }

    // 如果没有订单，返回空数据
    if (!ordersData || ordersData.length === 0) {
      return NextResponse.json({
        orders: [],
        stats: {
          totalOrders: 0,
          totalAmount: 0,
          topDishes: []
        }
      });
    }

    // 获取订单项目
    const orderIds = ordersData.map((order: any) => order.id);

    // 获取订单项目并关联菜品表以获取名称
    const { data: orderItemsData, error: orderItemsError } = await supabase
      .from('order_items')
      .select(`
        id,
        order_id,
        dish_id,
        quantity,
        price,
        status,
        dishes:dish_id (
          name_en,
          name_ja,
          name_ko,
          name_zh
        )
      `)
      .in('order_id', orderIds);

    if (orderItemsError) {
      throw orderItemsError;
    }

    // 处理订单项目数据，添加菜品名称
    const processedOrderItems = orderItemsData.map((item: any) => {
      // 根据当前语言环境选择菜品名称
      const dishName = item.dishes ? (
        item.dishes.name_en ||
        item.dishes.name_ja ||
        item.dishes.name_zh ||
        item.dishes.name_ko ||
        'Unknown Dish'
      ) : 'Unknown Dish';

      return {
        ...item,
        dish_name: dishName
      };
    });

    // 将订单项目添加到订单中
    const ordersWithItems = ordersData.map((order: any) => {
      const items = processedOrderItems.filter((item: any) => item.order_id === order.id);
      return {
        ...order,
        order_items: items || [],
      };
    });

    // 计算统计数据
    const totalOrders = ordersWithItems.length;
    let totalAmount = 0;
    const dishCounts: Record<string, { name: string, count: number, revenue: number }> = {};

    if (ordersWithItems.length > 0) {
      ordersWithItems.forEach((order: any) => {
        if (order.order_items && Array.isArray(order.order_items)) {
          order.order_items.forEach((item: any) => {
            // 计算总金额
            totalAmount += (item.price * item.quantity);

            // 统计菜品销量
            if (!dishCounts[item.dish_id]) {
              dishCounts[item.dish_id] = {
                name: item.dish_name,
                count: 0,
                revenue: 0
              };
            }
            dishCounts[item.dish_id].count += item.quantity;
            dishCounts[item.dish_id].revenue += (item.price * item.quantity);
          });
        }
      });
    }

    // 将菜品销量转换为数组并排序
    const topDishes = Object.values(dishCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // 只返回前10名

    return NextResponse.json({
      orders: ordersWithItems || [],
      stats: {
        totalOrders,
        totalAmount,
        topDishes
      }
    });
  } catch (error: any) {
    console.error('Error fetching order history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
