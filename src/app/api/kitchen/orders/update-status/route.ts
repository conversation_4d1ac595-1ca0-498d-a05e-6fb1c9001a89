import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { orderId, status } = body;

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Order ID and status are required' },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 更新订单状态
    const { error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId);

    if (error) {
      throw error;
    }

    // 获取更新后的完整订单信息以便通知厨房
    try {
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select(`
          *,
          tables(table_number),
          order_items(
            *,
            dishes(name_zh, name_ja, name_en, name_ko)
          )
        `)
        .eq('id', orderId)
        .single();

      if (!orderError && orderData) {
        // 处理订单数据以匹配前端期望的格式
        const processedOrderItems = orderData.order_items?.map((item: any) => {
          const dishName = item.dishes ? (
            item.dishes.name_zh ||
            item.dishes.name_ja ||
            item.dishes.name_en ||
            item.dishes.name_ko ||
            'Unknown Dish'
          ) : 'Unknown Dish';

          return {
            ...item,
            dish_name: dishName
          };
        }) || [];

        const processedOrder = {
          ...orderData,
          items: processedOrderItems,
          order_items: processedOrderItems // 保持兼容性
        };

        // 通过SSE通知厨房订单已更新
        broadcastToRestaurant(orderData.restaurant_id, 'order_updated', {
          order: processedOrder,
          timestamp: new Date().toISOString()
        });

        console.log(`SSE notification sent for updated order: ${orderId} to restaurant: ${orderData.restaurant_id}`);
      }
    } catch (sseError) {
      console.error('Error sending SSE notification for order update:', sseError);
      // 不要因为SSE通知失败而影响订单更新
    }

    return NextResponse.json({
      success: true,
      message: 'Order status updated successfully',
    });
  } catch (error: any) {
    console.error('Error updating order status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
