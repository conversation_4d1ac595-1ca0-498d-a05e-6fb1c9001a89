import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'restaurant_owner';
}

export async function GET(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const restaurantId = searchParams.get('restaurantId');

    // 构建查询
    let query = supabase
      .from('orders')
      .select(`
        *,
        tables(table_number)
      `)
      .not('status', 'eq', 'completed')
      .order('created_at', { ascending: false });

    // 如果提供了餐厅 ID，则按餐厅筛选
    if (restaurantId) {
      query = query.eq('restaurant_id', restaurantId);
    }

    // 执行查询
    const { data: ordersData, error: ordersError } = await query;

    if (ordersError) {
      throw ordersError;
    }

    // 如果没有订单，返回空数组
    if (!ordersData || ordersData.length === 0) {
      return NextResponse.json([]);
    }

    // 获取订单项目
    const orderIds = ordersData.map((order: any) => order.id);

    // 获取订单项目并关联菜品表以获取名称
    const { data: orderItemsData, error: orderItemsError } = await supabase
      .from('order_items')
      .select(`
        id,
        order_id,
        dish_id,
        quantity,
        price,
        status,
        dishes:dish_id (
          name_en,
          name_ja,
          name_ko,
          name_zh
        )
      `)
      .in('order_id', orderIds);

    if (orderItemsError) {
      throw orderItemsError;
    }

    // 处理订单项目数据，添加菜品名称
    const processedOrderItems = orderItemsData.map((item: any) => {
      // 根据当前语言环境选择菜品名称
      const dishName = item.dishes ? (
        item.dishes.name_en ||
        item.dishes.name_ja ||
        item.dishes.name_zh ||
        item.dishes.name_ko ||
        'Unknown Dish'
      ) : 'Unknown Dish';

      return {
        ...item,
        dish_name: dishName
      };
    });

    // 将订单项目添加到订单中
    const ordersWithItems = ordersData.map((order: any) => {
      const items = processedOrderItems.filter((item: any) => item.order_id === order.id);
      return {
        ...order,
        items: items || [],
        order_items: items || [], // 为了兼容性，同时提供 order_items 字段
      };
    });

    return NextResponse.json(ordersWithItems);
  } catch (error: any) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
