import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户信息和权限
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Please log in.' },
        { status: 401 }
      );
    }

    // 获取用户角色和餐厅信息
    const { data: userData, error } = await supabase
      .from('users')
      .select('role_id, restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (error || !userData) {
      return NextResponse.json(
        { error: 'User data not found.' },
        { status: 404 }
      );
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;
    const userRestaurantId = userData.restaurant_id;

    // 检查是否有厨房访问权限
    const hasKitchenAccess = roleName === 'super_admin' || roleName === 'admin' ||
                            roleName === 'kitchen' || roleName === 'restaurant_owner';

    if (!hasKitchenAccess) {
      return NextResponse.json(
        { error: 'Forbidden. Insufficient permissions.' },
        { status: 403 }
      );
    }

    // 构建查询 - 只查询用户所属餐厅的订单
    let query = supabase
      .from('orders')
      .select(`
        *,
        tables(table_number)
      `)
      .not('status', 'eq', 'completed')
      .order('created_at', { ascending: false });

    // 非超级管理员只能查看自己餐厅的订单
    if (roleName !== 'super_admin' && roleName !== 'admin') {
      if (!userRestaurantId) {
        return NextResponse.json(
          { error: 'User is not associated with any restaurant.' },
          { status: 403 }
        );
      }
      query = query.eq('restaurant_id', userRestaurantId);
    }

    // 执行查询
    const { data: ordersData, error: ordersError } = await query;

    if (ordersError) {
      throw ordersError;
    }

    // 如果没有订单，返回空数组
    if (!ordersData || ordersData.length === 0) {
      return NextResponse.json([]);
    }

    // 获取订单项目
    const orderIds = ordersData.map((order: any) => order.id);

    // 获取订单项目并关联菜品表以获取名称
    const { data: orderItemsData, error: orderItemsError } = await supabase
      .from('order_items')
      .select(`
        id,
        order_id,
        dish_id,
        quantity,
        price,
        status,
        dishes:dish_id (
          name_en,
          name_ja,
          name_ko,
          name_zh
        )
      `)
      .in('order_id', orderIds);

    if (orderItemsError) {
      throw orderItemsError;
    }

    // 处理订单项目数据，添加菜品名称
    const processedOrderItems = orderItemsData.map((item: any) => {
      // 根据当前语言环境选择菜品名称
      const dishName = item.dishes ? (
        item.dishes.name_en ||
        item.dishes.name_ja ||
        item.dishes.name_zh ||
        item.dishes.name_ko ||
        'Unknown Dish'
      ) : 'Unknown Dish';

      return {
        ...item,
        dish_name: dishName
      };
    });

    // 将订单项目添加到订单中
    const ordersWithItems = ordersData.map((order: any) => {
      const items = processedOrderItems.filter((item: any) => item.order_id === order.id);
      return {
        ...order,
        items: items || [],
        order_items: items || [], // 为了兼容性，同时提供 order_items 字段
      };
    });

    return NextResponse.json(ordersWithItems);
  } catch (error: any) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
