import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 存储活跃的SSE连接
const activeConnections = new Map<string, {
  controller: ReadableStreamDefaultController;
  restaurantId: string;
  lastHeartbeat: number;
}>();

// 心跳间隔（30秒）
const HEARTBEAT_INTERVAL = 30000;

// 清理过期连接的间隔（60秒）
const CLEANUP_INTERVAL = 60000;

// 定期清理过期连接
setInterval(() => {
  const now = Date.now();
  for (const [connectionId, connection] of activeConnections.entries()) {
    // 如果超过2分钟没有心跳，认为连接已断开
    if (now - connection.lastHeartbeat > 120000) {
      try {
        connection.controller.close();
      } catch (error) {
        console.error('Error closing expired connection:', error);
      }
      activeConnections.delete(connectionId);
      console.log(`Cleaned up expired connection: ${connectionId}`);
    }
  }
}, CLEANUP_INTERVAL);

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份并获取餐厅ID
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // 获取用户的餐厅ID
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('restaurant_id')
      .eq('id', user.id)
      .single();

    if (profileError || !profile?.restaurant_id) {
      return new Response('Restaurant not found', { status: 404 });
    }

    const restaurantId = profile.restaurant_id;
    const connectionId = `${restaurantId}_${Date.now()}_${Math.random()}`;

    console.log(`New SSE connection for restaurant: ${restaurantId}, connection: ${connectionId}`);

    // 创建SSE流
    const stream = new ReadableStream({
      start(controller) {
        // 存储连接信息
        activeConnections.set(connectionId, {
          controller,
          restaurantId,
          lastHeartbeat: Date.now()
        });

        // 发送初始连接确认
        controller.enqueue(`event: connected\n`);
        controller.enqueue(`data: {"restaurantId": "${restaurantId}", "connectionId": "${connectionId}"}\n\n`);

        // 设置心跳定时器
        const heartbeatTimer = setInterval(() => {
          try {
            // 发送心跳（keep-alive注释）
            controller.enqueue(`: heartbeat ${new Date().toISOString()}\n\n`);

            // 更新最后心跳时间
            const connection = activeConnections.get(connectionId);
            if (connection) {
              connection.lastHeartbeat = Date.now();
            }
          } catch (error) {
            console.error('Error sending heartbeat:', error);
            clearInterval(heartbeatTimer);
            activeConnections.delete(connectionId);
          }
        }, HEARTBEAT_INTERVAL);

        // 连接关闭时清理
        request.signal.addEventListener('abort', () => {
          console.log(`SSE connection closed: ${connectionId}`);
          clearInterval(heartbeatTimer);
          activeConnections.delete(connectionId);
          try {
            controller.close();
          } catch (error) {
            // 连接可能已经关闭
          }
        });
      },

      cancel() {
        console.log(`SSE connection cancelled: ${connectionId}`);
        activeConnections.delete(connectionId);
      }
    });

    // 返回SSE响应
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Accel-Buffering': 'no', // 禁用nginx缓冲
      },
    });

  } catch (error) {
    console.error('SSE endpoint error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// 广播消息到指定餐厅的所有连接
export function broadcastToRestaurant(restaurantId: string, event: string, data: any) {
  console.log(`Broadcasting ${event} to restaurant ${restaurantId}. Active connections: ${activeConnections.size}`);

  const message = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
  let broadcastCount = 0;

  for (const [connectionId, connection] of activeConnections.entries()) {
    console.log(`Checking connection ${connectionId} for restaurant ${connection.restaurantId}`);
    if (connection.restaurantId === restaurantId) {
      try {
        connection.controller.enqueue(message);
        broadcastCount++;
        console.log(`✅ Broadcasted ${event} to connection: ${connectionId}`);
      } catch (error) {
        console.error(`❌ Error broadcasting to connection ${connectionId}:`, error);
        // 移除失效的连接
        activeConnections.delete(connectionId);
      }
    }
  }

  console.log(`Broadcast complete. Sent to ${broadcastCount} connections for restaurant ${restaurantId}`);
}

// 获取活跃连接统计
export function getConnectionStats() {
  const stats = new Map<string, number>();
  for (const connection of activeConnections.values()) {
    const count = stats.get(connection.restaurantId) || 0;
    stats.set(connection.restaurantId, count + 1);
  }
  return {
    totalConnections: activeConnections.size,
    restaurantConnections: Object.fromEntries(stats)
  };
}
