import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { restaurantId, tableId, guestCount, items, orderType } = body;

    if (!restaurantId || !tableId || !items || !Array.isArray(items) || items.length === 0) {
      console.error('Invalid request data:', { restaurantId, tableId, itemsLength: items?.length });
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    // 记录请求信息
    console.log('Manual order request:', {
      restaurantId,
      tableId,
      guestCount,
      orderType,
      itemsCount: items.length
    });

    // 检查是否有进行中的订单（如果是追加模式）
    let orderId;

    if (orderType === 'append') {
      // 查找该餐桌的进行中订单
      const { data: existingOrders, error: findError } = await supabase
        .from('orders')
        .select('id')
        .eq('table_id', tableId)
        .eq('restaurant_id', restaurantId)
        .in('status', ['pending', 'in_progress'])
        .order('created_at', { ascending: false })
        .limit(1);

      if (findError) {
        console.error('Error finding existing order:', findError);
        return NextResponse.json(
          { error: 'Failed to find existing order' },
          { status: 500 }
        );
      }

      // 如果找到进行中的订单，使用它
      if (existingOrders && existingOrders.length > 0) {
        orderId = existingOrders[0].id;
        console.log('Appending to existing order:', orderId);
      }
    }

    // 如果没有找到进行中的订单或者是新订单模式，创建新订单
    if (!orderId) {
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          restaurant_id: restaurantId,
          table_id: tableId,
          status: 'pending',
          guest_count: guestCount || 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (orderError) {
        console.error('Error creating order:', orderError);
        return NextResponse.json(
          { error: 'Failed to create order' },
          { status: 500 }
        );
      }

      orderId = order.id;
    }

    // 创建订单项目
    const orderItems = items.map((item: any) => ({
      order_id: orderId,
      dish_id: item.dishId,
      quantity: item.quantity,
      price: item.price,
      status: 'pending',
      item_type: orderType || 'new' // 使用传入的订单类型，默认为 'new'
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItems);

    if (itemsError) {
      console.error('Error creating order items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to create order items' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      orderId: orderId
    });
  } catch (error: any) {
    console.error('Error creating manual order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
