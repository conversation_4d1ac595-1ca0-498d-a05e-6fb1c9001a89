import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getLocalizedName } from '@/lib/i18n-utils';
import { startOfDay, endOfDay } from 'date-fns';
import { requireKitchenAccess } from '@/lib/auth/permissions';

export async function GET(request: Request) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || 'en'; // 默认英文

    // 验证用户权限
    const permissions = await requireKitchenAccess(request as any);

    if (permissions instanceof NextResponse) {
      return permissions; // 返回错误响应
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取今天的开始和结束时间
    const today = new Date();
    const startOfToday = startOfDay(today);
    const endOfToday = endOfDay(today);

    // 构建查询 - 只查询用户所属餐厅的订单
    let query = supabase
      .from('orders')
      .select('*')
      .gte('created_at', startOfToday.toISOString())
      .lte('created_at', endOfToday.toISOString());

    // 非超级管理员只能查看自己餐厅的订单
    if (permissions.roleName !== 'super_admin' && permissions.roleName !== 'admin') {
      if (!permissions.restaurantId) {
        return NextResponse.json(
          { error: 'User is not associated with any restaurant.' },
          { status: 403 }
        );
      }
      query = query.eq('restaurant_id', permissions.restaurantId);
    }

    // 获取今天的订单
    const { data: todayOrders, error: ordersError } = await query;

    if (ordersError) {
      throw ordersError;
    }

    // 如果没有订单，返回空数据
    if (!todayOrders || todayOrders.length === 0) {
      return NextResponse.json({
        totalOrders: 0,
        totalAmount: 0,
        topDishes: []
      });
    }

    // 获取订单项目
    const orderIds = todayOrders.map((order: any) => order.id);

    // 获取订单项目并关联菜品表以获取名称
    const { data: orderItemsData, error: orderItemsError } = await supabase
      .from('order_items')
      .select(`
        id,
        order_id,
        dish_id,
        quantity,
        price,
        status,
        dishes:dish_id (
          name_en,
          name_ja,
          name_ko,
          name_zh
        )
      `)
      .in('order_id', orderIds);

    if (orderItemsError) {
      throw orderItemsError;
    }

    // 处理订单项目数据，添加菜品名称
    const processedOrderItems = orderItemsData.map((item: any) => {
      // 根据用户语言环境选择菜品名称
      const dishName = item.dishes ? getLocalizedName(item.dishes, locale, 'Unknown Dish') : 'Unknown Dish';

      return {
        ...item,
        dish_name: dishName
      };
    });

    // 将订单项目添加到订单中
    const ordersWithItems = todayOrders.map((order: any) => {
      const items = processedOrderItems.filter((item: any) => item.order_id === order.id);
      return {
        ...order,
        order_items: items || [],
      };
    });

    // 计算统计数据
    const totalOrders = ordersWithItems.length;
    let totalAmount = 0;
    const dishCounts: Record<string, { name: string, count: number, revenue: number }> = {};

    if (ordersWithItems.length > 0) {
      ordersWithItems.forEach((order: any) => {
        if (order.order_items && Array.isArray(order.order_items)) {
          order.order_items.forEach((item: any) => {
            // 计算总金额
            totalAmount += (item.price * item.quantity);

            // 统计菜品销量
            if (!dishCounts[item.dish_id]) {
              dishCounts[item.dish_id] = {
                name: item.dish_name,
                count: 0,
                revenue: 0
              };
            }
            dishCounts[item.dish_id].count += item.quantity;
            dishCounts[item.dish_id].revenue += (item.price * item.quantity);
          });
        }
      });
    }

    // 将菜品销量转换为数组并排序
    const topDishes = Object.values(dishCounts)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // 只返回前5名

    return NextResponse.json({
      totalOrders,
      totalAmount,
      topDishes
    });
  } catch (error: any) {
    console.error('Error fetching kitchen stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
