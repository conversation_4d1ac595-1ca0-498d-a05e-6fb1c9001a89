import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { translateCategoryName } from '@/lib/translation';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

// 创建新类别
export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { name, inputLanguage, sortOrder, restaurantId } = body;

    if (!name || !inputLanguage || !restaurantId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 使用 AI 翻译类别名称
    console.log(`Translating category name from ${inputLanguage}:`, name);

    let translations;
    try {
      translations = await translateCategoryName(name, inputLanguage);
      console.log('Translation results received:', JSON.stringify(translations, null, 2));

      // 验证翻译结果
      if (!translations || typeof translations !== 'object') {
        console.error('Invalid translation result format:', translations);
        throw new Error('Invalid translation result format');
      }

      // 检查是否所有语言都有翻译
      const missingTranslations = [];
      if (!translations.en) missingTranslations.push('en');
      if (!translations.zh) missingTranslations.push('zh');
      if (!translations.ja) missingTranslations.push('ja');
      if (!translations.ko) missingTranslations.push('ko');

      if (missingTranslations.length > 0) {
        console.warn(`Missing translations for languages: ${missingTranslations.join(', ')}`);
      }
    } catch (error: any) {
      console.error('Error during translation:', error);
      return NextResponse.json(
        { error: 'Translation service error' },
        { status: 500 }
      );
    }

    // 准备类别数据
    const categoryData = {
      restaurant_id: restaurantId,
      name_zh: inputLanguage === 'zh' ? name : translations.zh,
      name_en: translations.en,
      name_ja: inputLanguage === 'ja' ? name : translations.ja,
      name_ko: translations.ko,
      sort_order: sortOrder || 0
    };

    // 创建类别
    const { data: category, error: categoryError } = await supabase
      .from('categories')
      .insert(categoryData)
      .select()
      .single();

    if (categoryError) {
      console.error('Error creating category:', categoryError);
      return NextResponse.json(
        { error: 'Failed to create category: ' + categoryError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: category
    });
  } catch (error: any) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
