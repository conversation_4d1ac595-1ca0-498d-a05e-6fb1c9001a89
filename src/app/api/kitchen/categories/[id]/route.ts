import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { translateCategoryName } from '@/lib/translation';

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

// 更新类别
export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: categoryId } = await params;

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { name, inputLanguage, sortOrder, restaurantId } = body;

    if (!name || !inputLanguage) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 检查类别是否存在
    const { data: existingCategory, error: fetchError } = await supabase
      .from('categories')
      .select('*')
      .eq('id', categoryId)
      .single();

    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // 使用 AI 翻译类别名称
    console.log(`Translating category name from ${inputLanguage}:`, name);

    let translations;
    try {
      translations = await translateCategoryName(name, inputLanguage);
      console.log('Translation results received:', JSON.stringify(translations, null, 2));

      // 验证翻译结果
      if (!translations || typeof translations !== 'object') {
        console.error('Invalid translation result format:', translations);
        throw new Error('Invalid translation result format');
      }

      // 检查是否所有语言都有翻译
      const missingTranslations = [];
      if (!translations.en) missingTranslations.push('en');
      if (!translations.zh) missingTranslations.push('zh');
      if (!translations.ja) missingTranslations.push('ja');
      if (!translations.ko) missingTranslations.push('ko');

      if (missingTranslations.length > 0) {
        console.warn(`Missing translations for languages: ${missingTranslations.join(', ')}`);
      }
    } catch (error: any) {
      console.error('Error during translation:', error);
      return NextResponse.json(
        { error: 'Translation service error' },
        { status: 500 }
      );
    }

    // 准备更新数据
    const updateData = {
      name_zh: inputLanguage === 'zh' ? name : translations.zh,
      name_en: translations.en,
      name_ja: inputLanguage === 'ja' ? name : translations.ja,
      name_ko: translations.ko,
      sort_order: sortOrder || 0
    };

    // 更新类别
    const { data: updatedCategory, error: updateError } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', categoryId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating category:', updateError);
      return NextResponse.json(
        { error: 'Failed to update category: ' + updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedCategory
    });
  } catch (error: any) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 删除类别
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: categoryId } = await params;

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 检查类别是否存在
    const { data: existingCategory, error: fetchError } = await supabase
      .from('categories')
      .select('*')
      .eq('id', categoryId)
      .single();

    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      );
    }

    // 检查是否有关联的菜品
    const { count, error: countError } = await supabase
      .from('dish_categories')
      .select('*', { count: 'exact', head: true })
      .eq('category_id', categoryId);

    if (countError) {
      console.error('Error checking related dishes:', countError);
      return NextResponse.json(
        { error: 'Failed to check related dishes' },
        { status: 500 }
      );
    }

    if (count && count > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with associated dishes' },
        { status: 400 }
      );
    }

    // 删除类别
    const { error: deleteError } = await supabase
      .from('categories')
      .delete()
      .eq('id', categoryId);

    if (deleteError) {
      console.error('Error deleting category:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete category: ' + deleteError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true
    });
  } catch (error: any) {
    console.error('Error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
