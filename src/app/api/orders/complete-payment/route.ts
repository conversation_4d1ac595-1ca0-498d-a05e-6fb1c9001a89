import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // 尝试解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }
    
    const { orderId, paymentMethod } = body;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // 获取订单信息
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('*, order_items(*), tables(table_number)')
      .eq('id', orderId)
      .single();

    if (orderError || !order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // 计算订单总金额
    const totalAmount = order.order_items.reduce(
      (total, item) => total + (item.price * item.quantity),
      0
    );

    // 更新订单状态为已完成，并记录支付信息
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        status: 'completed',
        payment_method: paymentMethod || 'unknown',
        payment_status: 'paid',
        payment_time: new Date().toISOString(),
        total_amount: totalAmount,
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update order status' },
        { status: 500 }
      );
    }

    // 更新所有订单项目的状态为已完成
    const { error: itemsUpdateError } = await supabase
      .from('order_items')
      .update({
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('order_id', orderId);

    if (itemsUpdateError) {
      return NextResponse.json(
        { error: 'Failed to update order items status' },
        { status: 500 }
      );
    }

    // 记录订单分析数据
    // 这里可以添加更多的分析数据，如时间段、顾客信息等
    const analyticsData = {
      order_id: orderId,
      restaurant_id: order.restaurant_id,
      table_id: order.table_id,
      table_number: order.tables.table_number,
      total_amount: totalAmount,
      items_count: order.order_items.length,
      payment_method: paymentMethod || 'unknown',
      payment_time: new Date().toISOString(),
      order_time: order.created_at,
      // 可以添加更多分析数据
      day_of_week: new Date().getDay(),
      hour_of_day: new Date().getHours(),
      items_data: JSON.stringify(order.order_items.map(item => ({
        dish_id: item.dish_id,
        quantity: item.quantity,
        price: item.price
      })))
    };

    // 将分析数据存储到数据库
    // 注意：这里假设我们已经创建了order_analytics表
    // 如果表不存在，这个操作会失败
    try {
      const { error: analyticsError } = await supabase
        .from('order_analytics')
        .insert(analyticsData);

      if (analyticsError) {
        console.error('Failed to save analytics data:', analyticsError);
        // 不要因为分析数据保存失败而中断整个流程
      }
    } catch (error: any) {
      console.error('Error saving analytics data:', error);
      // 如果表不存在，这里会捕获错误
    }

    return NextResponse.json({
      success: true,
      message: 'Order payment completed successfully',
      order: {
        id: orderId,
        status: 'completed',
        total_amount: totalAmount,
        payment_time: new Date().toISOString()
      }
    });
  } catch (error: any) {
    console.error('Error completing order payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
