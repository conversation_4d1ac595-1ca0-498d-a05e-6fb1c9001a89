import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 查询用户信息
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('*, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (userDataError) {
      console.error('Error fetching user data:', userDataError);
      return NextResponse.json({ error: userDataError.message }, { status: 500 });
    }

    if (!userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // 返回用户信息
    return NextResponse.json({ data: userData });
  } catch (error: any) {
    console.error('Error in users API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { authId, userData } = body;

    if (!authId || !userData) {
      return NextResponse.json(
        { error: 'Invalid request. authId and userData are required.' },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 检查当前用户是否是超级管理员
    const { data: currentUserData, error: currentUserError } = await supabase
      .from('users')
      .select('roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (currentUserError) {
      return NextResponse.json(
        { error: 'Error fetching current user data: ' + currentUserError.message },
        { status: 500 }
      );
    }

    // @ts-ignore - 类型定义问题
    const roleName = currentUserData?.roles?.name;

    // 只有超级管理员或者用户自己才能更新用户信息
    if (roleName !== 'super_admin' && user.id !== authId) {
      return NextResponse.json(
        { error: 'Forbidden. Only super_admin or the user themselves can update user data.' },
        { status: 403 }
      );
    }

    // 更新用户信息
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update(userData)
      .eq('auth_id', authId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { error: 'Error updating user: ' + updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: updatedUser });
  } catch (error: any) {
    console.error('Error in users API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
