import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('Simple SSE endpoint called');
  
  try {
    const stream = new ReadableStream({
      start(controller) {
        console.log('SSE stream started');
        
        // 发送初始消息
        controller.enqueue('data: {"message": "Connected to simple SSE"}\n\n');
        
        // 每5秒发送一条消息
        const interval = setInterval(() => {
          try {
            const message = `data: {"timestamp": "${new Date().toISOString()}", "message": "Hello from SSE"}\n\n`;
            controller.enqueue(message);
            console.log('Sent SSE message');
          } catch (error) {
            console.error('Error sending SSE message:', error);
            clearInterval(interval);
          }
        }, 5000);
        
        // 清理
        request.signal.addEventListener('abort', () => {
          console.log('SSE connection aborted');
          clearInterval(interval);
          try {
            controller.close();
          } catch (error) {
            console.error('Error closing controller:', error);
          }
        });
      },
      
      cancel() {
        console.log('SSE stream cancelled');
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('Error in simple SSE endpoint:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
