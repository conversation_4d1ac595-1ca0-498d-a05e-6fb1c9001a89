import { NextRequest, NextResponse } from 'next/server';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import { s3Client, bucketName, allowedFileTypes, maxFileSize, getPublicUrl } from '@/lib/r2';
import { createClient } from '@/lib/supabase/server';

// 验证用户是否有权限上传文件
async function validateUploadAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证上传权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有上传权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateUploadAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    const { fileType, fileSize, fileName } = body;

    // 验证文件类型
    if (!allowedFileTypes.includes(fileType)) {
      return NextResponse.json(
        { error: 'File type not allowed' },
        { status: 400 }
      );
    }

    // 验证文件大小
    if (fileSize > maxFileSize) {
      return NextResponse.json(
        { error: 'File size exceeds the limit' },
        { status: 400 }
      );
    }

    // 生成唯一的文件名
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    const key = `dishes/${uniqueFileName}`;

    // 创建 PutObjectCommand
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      ContentType: fileType,
    });

    // 生成预签名 URL（有效期 10 分钟）
    const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn: 600 });

    // 生成公共访问 URL
    const publicUrl = getPublicUrl(key);

    return NextResponse.json({
      presignedUrl,
      publicUrl,
      key
    });
  } catch (error: any) {
    console.error('Error generating presigned URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate presigned URL' },
      { status: 500 }
    );
  }
}
