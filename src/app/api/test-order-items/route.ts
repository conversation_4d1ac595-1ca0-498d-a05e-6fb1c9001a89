import { NextRequest, NextResponse } from 'next/server';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

/**
 * 测试订单项插入
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { orderItems } = body;

    if (!orderItems || !Array.isArray(orderItems) || orderItems.length === 0) {
      return NextResponse.json({ error: 'Order items are required' }, { status: 400 });
    }

    console.log('Testing order items insertion:', orderItems);

    // 创建 Supabase 客户端 (使用anon角色)
    const supabaseUrl = process.env.NEXT_SUPABASE_URL || process.env.SUPABASE_URL!;
    const supabaseAnonKey = process.env.NEXT_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY!;
    const anonSupabase = createSupabaseClient(supabaseUrl, supabaseAnonKey);

    console.log('Supabase client created with anon key');

    // 首先测试能否访问orders表
    const { data: orderTest, error: orderTestError } = await anonSupabase
      .from('orders')
      .select('id, status')
      .eq('id', orderItems[0].order_id)
      .single();

    console.log('Order test result:', { orderTest, orderTestError });

    // 测试能否访问dishes表
    const { data: dishTest, error: dishTestError } = await anonSupabase
      .from('dishes')
      .select('id, name_zh')
      .eq('id', orderItems[0].dish_id)
      .single();

    console.log('Dish test result:', { dishTest, dishTestError });

    // 尝试插入订单项
    const { data, error } = await anonSupabase
      .from('order_items')
      .insert(orderItems)
      .select();

    console.log('Insert result:', { data, error });

    if (error) {
      console.error('Error adding order items:', error);
      return NextResponse.json({ 
        error: error.message, 
        details: error,
        orderTest,
        dishTest 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      data,
      orderTest,
      dishTest 
    });
  } catch (error: any) {
    console.error('Error in test order items API:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
