import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // 获取餐厅 ID 参数
    const { searchParams } = new URL(request.url);
    const restaurantId = searchParams.get('restaurantId');

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 查询菜品信息，按餐厅 ID 筛选
    const { data, error } = await supabase
      .from('dishes')
      .select('*')
      .eq('restaurant_id', restaurantId);

    if (error) {
      console.error('Error fetching dishes:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // 返回菜品信息
    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in dishes API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
