import { Server as SocketIOServer } from 'socket.io';
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// 存储所有活跃的Socket.IO服务器实例
let io: SocketIOServer | null = null;

// 存储最后一次获取的订单数据
let lastOrdersData: any[] = [];

// 验证用户是否有权限访问厨房数据
async function validateKitchenAccess(supabase: any) {
  // 使用 getUser 代替 getSession 获取经过验证的用户信息
  const { data, error: userError } = await supabase.auth.getUser();

  if (userError || !data.user) {
    console.log('验证厨房访问权限 - 获取用户错误:', userError);
    return false;
  }

  const user = data.user;

  // 获取用户角色
  const { data: userData, error } = await supabase
    .from('users')
    .select('role_id, roles:role_id(name)')
    .eq('auth_id', user.id)
    .single();

  if (error || !userData) {
    return false;
  }

  // @ts-ignore - 类型定义问题
  const roleName = userData.roles?.name;

  // 检查是否有厨房访问权限
  return roleName === 'super_admin' || roleName === 'admin' || roleName === 'kitchen';
}

// 获取订单数据
async function fetchOrders(supabase: any, restaurantId?: string) {
  try {
    // 构建查询
    let query = supabase
      .from('orders')
      .select(`
        *,
        tables(table_number)
      `)
      .not('status', 'eq', 'completed')
      .order('created_at', { ascending: false });

    // 如果提供了餐厅 ID，则按餐厅筛选
    if (restaurantId) {
      query = query.eq('restaurant_id', restaurantId);
    }

    // 执行查询
    const { data: ordersData, error: ordersError } = await query;

    if (ordersError) {
      throw ordersError;
    }

    // 如果没有订单，返回空数组
    if (!ordersData || ordersData.length === 0) {
      return [];
    }

    // 获取订单项目
    const orderIds = ordersData.map((order: any) => order.id);

    // 获取订单项目并关联菜品表以获取名称
    const { data: orderItemsData, error: orderItemsError } = await supabase
      .from('order_items')
      .select(`
        id,
        order_id,
        dish_id,
        quantity,
        price,
        status,
        dishes:dish_id (
          name_en,
          name_ja,
          name_ko,
          name_zh
        )
      `)
      .in('order_id', orderIds);

    if (orderItemsError) {
      throw orderItemsError;
    }

    // 处理订单项目数据，添加菜品名称
    const processedOrderItems = orderItemsData.map((item: any) => {
      // 根据当前语言环境选择菜品名称
      const dishName = item.dishes ? (
        item.dishes.name_en ||
        item.dishes.name_ja ||
        item.dishes.name_zh ||
        item.dishes.name_ko ||
        'Unknown Dish'
      ) : 'Unknown Dish';

      return {
        ...item,
        dish_name: dishName
      };
    });

    // 将订单项目添加到订单中
    const ordersWithItems = ordersData.map((order: any) => {
      const items = processedOrderItems.filter((item: any) => item.order_id === order.id);
      return {
        ...order,
        items: items || [],
        order_items: items || [], // 为了兼容性，同时提供 order_items 字段
      };
    });

    return ordersWithItems || [];
  } catch (error: any) {
    console.error('Error fetching orders:', error);
    return [];
  }
}

// 设置Supabase实时订阅
async function setupRealtimeSubscription(supabase: any, io: SocketIOServer) {
  // 订阅orders表的变化
  const ordersChannel = supabase
    .channel('orders-changes')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'orders',
      },
      async () => {
        // 当orders表发生变化时，获取最新数据并广播给所有客户端
        const supabase = await createClient();
        const newOrdersData = await fetchOrders(supabase);

        // 只有当数据发生变化时才广播
        if (JSON.stringify(newOrdersData) !== JSON.stringify(lastOrdersData)) {
          lastOrdersData = newOrdersData;
          io.emit('orders-update', newOrdersData);
        }
      }
    )
    .subscribe();

  // 订阅order_items表的变化
  const orderItemsChannel = supabase
    .channel('order-items-changes')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'order_items',
      },
      async () => {
        // 当order_items表发生变化时，获取最新数据并广播给所有客户端
        const supabase = await createClient();
        const newOrdersData = await fetchOrders(supabase);

        // 只有当数据发生变化时才广播
        if (JSON.stringify(newOrdersData) !== JSON.stringify(lastOrdersData)) {
          lastOrdersData = newOrdersData;
          io.emit('orders-update', newOrdersData);
        }
      }
    )
    .subscribe();

  return { ordersChannel, orderItemsChannel };
}

export async function GET(req: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 验证用户权限
    const hasAccess = await validateKitchenAccess(supabase);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 如果Socket.IO服务器已经存在，直接返回成功
    if (io) {
      return NextResponse.json({ success: true });
    }

    // 获取当前的响应对象
    const res = new NextResponse();

    // 创建Socket.IO服务器
    // @ts-ignore - 类型定义问题
    io = new SocketIOServer(res.socket?.server, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    });

    // 设置Socket.IO连接事件
    io.on('connection', async (socket) => {
      console.log('Client connected:', socket.id);

      // 获取初始订单数据并发送给客户端
      const initialOrdersData = await fetchOrders(supabase);
      lastOrdersData = initialOrdersData;
      socket.emit('orders-update', initialOrdersData);

      // 处理客户端请求获取订单数据
      socket.on('get-orders', async (restaurantId?: string) => {
        const ordersData = await fetchOrders(supabase, restaurantId);
        socket.emit('orders-update', ordersData);
      });

      // 处理客户端断开连接
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });

    // 设置Supabase实时订阅
    const { ordersChannel, orderItemsChannel } = await setupRealtimeSubscription(supabase, io);

    console.log('Socket.IO server and Supabase subscriptions initialized');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error initializing Socket.IO server:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
