import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * 创建新餐厅
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json({ error: 'Restaurant name is required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取会话，验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession();
    
    // 如果没有会话，返回未授权错误
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 验证用户角色
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role_id, roles:role_id(name)')
      .eq('auth_id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;

    // 只有超级管理员和管理员可以创建餐厅
    if (roleName !== 'super_admin' && roleName !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // 创建餐厅
    const { data, error } = await supabase
      .from('restaurants')
      .insert({ name: name.trim() })
      .select()
      .single();

    if (error) {
      console.error('Error creating restaurant:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error in restaurants API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
