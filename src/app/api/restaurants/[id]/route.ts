import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * 获取单个餐厅信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const restaurantId = resolvedParams.id;

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取会话，验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession();
    
    // 如果没有会话，返回未授权错误
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 查询餐厅信息
    const { data, error } = await supabase
      .from('restaurants')
      .select('*')
      .eq('id', restaurantId)
      .single();

    if (error) {
      console.error('Error fetching restaurant:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    if (!data) {
      return NextResponse.json({ error: 'Restaurant not found' }, { status: 404 });
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in restaurants API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * 更新餐厅信息
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string } >}
) {
  try {
    const { id: restaurantId } = await params;

    const body = await request.json();
    const { name } = body;

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    if (!name) {
      return NextResponse.json({ error: 'Restaurant name is required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取会话，验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession();
    
    // 如果没有会话，返回未授权错误
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 验证用户角色
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role_id, roles:role_id(name)')
      .eq('auth_id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;

    // 只有超级管理员和管理员可以更新餐厅
    if (roleName !== 'super_admin' && roleName !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // 更新餐厅
    const { data, error } = await supabase
      .from('restaurants')
      .update({ name: name.trim() })
      .eq('id', restaurantId)
      .select()
      .single();

    if (error) {
      console.error('Error updating restaurant:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error('Error in restaurants API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * 删除餐厅
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    
    const { id: restaurantId }= await params;

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取会话，验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession();
    
    // 如果没有会话，返回未授权错误
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 验证用户角色
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role_id, roles:role_id(name)')
      .eq('auth_id', session.user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData.roles?.name;

    // 只有超级管理员可以删除餐厅
    if (roleName !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // 删除餐厅
    const { error } = await supabase
      .from('restaurants')
      .delete()
      .eq('id', restaurantId);

    if (error) {
      console.error('Error deleting restaurant:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in restaurants API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
