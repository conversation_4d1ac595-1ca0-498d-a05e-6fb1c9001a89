import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client
    const supabase = await createClient();

    // Count restaurants
    const { count, error } = await supabase
      .from('restaurants')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error counting restaurants:', error);
      return NextResponse.json(
        { error: 'Failed to count restaurants' },
        { status: 500 }
      );
    }

    // Return the count
    return NextResponse.json({
      count: count || 0
    });
  } catch (error: any) {
    console.error('Error in restaurants count API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
