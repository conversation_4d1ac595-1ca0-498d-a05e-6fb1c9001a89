import { NextRequest, NextResponse } from 'next/server';
import { broadcastToRestaurant, getConnectionStats } from '@/app/api/kitchen/sse/route';

/**
 * 测试SSE广播功能
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { restaurantId, event = 'test_event', message = 'Test message' } = body;

    if (!restaurantId) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }

    console.log(`Testing SSE broadcast to restaurant: ${restaurantId}`);

    // 获取连接统计
    const stats = getConnectionStats();
    console.log('Connection stats:', stats);

    // 广播测试消息
    broadcastToRestaurant(restaurantId, event, {
      message,
      timestamp: new Date().toISOString(),
      test: true
    });

    return NextResponse.json({
      success: true,
      restaurantId,
      event,
      message,
      connectionStats: stats
    });
  } catch (error: any) {
    console.error('Error testing SSE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * 获取SSE连接统计
 */
export async function GET() {
  try {
    const stats = getConnectionStats();
    return NextResponse.json(stats);
  } catch (error: any) {
    console.error('Error getting SSE stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
