// import { NextRequest, NextResponse } from 'next/server';
// import { createClient } from '@/lib/supabase/server';

// /**
//  * 获取单个表格信息
//  */
// export async function GET(
//   request: NextRequest,
//   { params }: { params: Promise<{ id: string }> }
// ) {
//   try {
//     const { id: tableId } = await params;

//     if (!tableId) {
//       return NextResponse.json({ error: 'Table ID is required' }, { status: 400 });
//     }

//     // 创建 Supabase 客户端
//     const supabase = await createClient();
//     console.log('tableId', tableId);

//     // 查询表格信息
//     const { data, error } = await supabase
//       .from('tables')
//       .select('*')
//       .eq('id', tableId)
//       .single();

//     if (error) {
//       console.error('tableId', tableId);
//       console.error('Error fetching table！！:', error);
//       return NextResponse.json({ error: error.message }, { status: 500 });
//     }

//     if (!data) {
//       return NextResponse.json({ error: 'Table not found' }, { status: 404 });
//     }

//     return NextResponse.json(data);
//   } catch (error: any) {
//     console.error('Error in tables API:', error);
//     return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
//   }
// }
