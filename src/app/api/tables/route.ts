import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // 获取请求参数
    const { searchParams } = new URL(request.url);
    const tableId = searchParams.get('id');
    const restaurantId = searchParams.get('restaurantId');

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 如果提供了表格 ID，则获取单个表格信息
    console.log('tableId', tableId,tableId?.length);
    if (tableId) {
      // 查询表格信息
      const { data, error } = await supabase
        .from('tables')
        .select('*')
        .eq('id', tableId)
        .single();

      if (error) {
        console.error('Error fetching table:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      if (!data) {
        return NextResponse.json({ error: 'Table not found' }, { status: 404 });
      }

      // 返回表格信息
      return NextResponse.json({ data });
    }

    // 如果提供了餐厅 ID，则获取该餐厅的所有表格
    else if (restaurantId) {
      console.log('API - Fetching tables for restaurant ID:', restaurantId);

      // 获取当前用户
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        return NextResponse.json(
          { error: 'Unauthorized. User not authenticated.' },
          { status: 401 }
        );
      }

      // 查询表格信息
      const { data, error } = await supabase
        .from('tables')
        .select('*')
        .eq('restaurant_id', restaurantId)
        .order('table_number');

      if (error) {
        console.error('API - Error fetching tables:', error);
        return NextResponse.json({ error: error.message }, { status: 500 });
      }

      console.log('API - Fetched tables count:', data?.length || 0);

      // 返回表格信息
      return NextResponse.json({ data });
    }

    // 如果没有提供表格 ID 或餐厅 ID，则返回错误
    else {
      return NextResponse.json(
        { error: 'Either table ID or restaurant ID is required' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error in tables API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { restaurantId, tableNumbers } = body;

    if (!restaurantId || !tableNumbers || !Array.isArray(tableNumbers)) {
      return NextResponse.json(
        { error: 'Invalid request. restaurantId and tableNumbers array are required.' },
        { status: 400 }
      );
    }

    // 创建服务器端 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 检查用户是否有权限管理该餐厅的表格
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (userDataError) {
      return NextResponse.json(
        { error: 'Error fetching user data: ' + userDataError.message },
        { status: 500 }
      );
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData?.roles?.name;
    const userRestaurantId = userData?.restaurant_id;

    // 检查用户是否是超级管理员、管理员或厨房角色，以及是否属于该餐厅
    const isAdmin = roleName === 'super_admin' || roleName === 'admin';
    const isKitchen = roleName === 'kitchen';
    const isSameRestaurant = userRestaurantId === restaurantId;

    if (!(isAdmin || (isKitchen && isSameRestaurant))) {
      return NextResponse.json(
        { error: 'Forbidden. User does not have permission to manage tables for this restaurant.' },
        { status: 403 }
      );
    }

    // 准备表格数据
    const tablesToCreate = tableNumbers.map((tableNumber: string | number) => ({
      restaurant_id: restaurantId,
      table_number: tableNumber.toString(),
    }));

    console.log('Creating tables:', tablesToCreate);

    // 创建表格
    const { data: createdTables, error: createError } = await supabase
      .from('tables')
      .insert(tablesToCreate)
      .select();

    if (createError) {
      return NextResponse.json(
        { error: 'Error creating tables: ' + createError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: createdTables });
  } catch (error: any) {
    console.error('Error in tables API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { tableId, tableNumber } = body;

    if (!tableId || !tableNumber) {
      return NextResponse.json(
        { error: 'Invalid request. tableId and tableNumber are required.' },
        { status: 400 }
      );
    }

    // 创建服务器端 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 获取表格信息，确认表格存在
    const { data: tableData, error: tableError } = await supabase
      .from('tables')
      .select('restaurant_id')
      .eq('id', tableId)
      .single();

    if (tableError) {
      return NextResponse.json(
        { error: 'Error fetching table: ' + tableError.message },
        { status: 500 }
      );
    }

    if (!tableData) {
      return NextResponse.json({ error: 'Table not found' }, { status: 404 });
    }

    // 检查用户是否有权限管理该餐厅的表格
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (userDataError) {
      return NextResponse.json(
        { error: 'Error fetching user data: ' + userDataError.message },
        { status: 500 }
      );
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData?.roles?.name;
    const userRestaurantId = userData?.restaurant_id;
    const tableRestaurantId = tableData.restaurant_id;

    // 检查用户是否是超级管理员、管理员或厨房角色，以及是否属于该餐厅
    const isAdmin = roleName === 'super_admin' || roleName === 'admin';
    const isKitchen = roleName === 'kitchen';
    const isSameRestaurant = userRestaurantId === tableRestaurantId;

    if (!(isAdmin || (isKitchen && isSameRestaurant))) {
      return NextResponse.json(
        { error: 'Forbidden. User does not have permission to manage this table.' },
        { status: 403 }
      );
    }

    // 更新表格
    const { data: updatedTable, error: updateError } = await supabase
      .from('tables')
      .update({ table_number: tableNumber.toString() })
      .eq('id', tableId)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { error: 'Error updating table: ' + updateError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: updatedTable });
  } catch (error: any) {
    console.error('Error in tables API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // 获取请求参数
    const { searchParams } = new URL(request.url);
    const tableId = searchParams.get('id');

    if (!tableId) {
      return NextResponse.json(
        { error: 'Invalid request. Table ID is required.' },
        { status: 400 }
      );
    }

    // 创建服务器端 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 获取表格信息，确认表格存在
    const { data: tableData, error: tableError } = await supabase
      .from('tables')
      .select('restaurant_id')
      .eq('id', tableId)
      .single();

    if (tableError) {
      return NextResponse.json(
        { error: 'Error fetching table: ' + tableError.message },
        { status: 500 }
      );
    }

    if (!tableData) {
      return NextResponse.json({ error: 'Table not found' }, { status: 404 });
    }

    // 检查用户是否有权限管理该餐厅的表格
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (userDataError) {
      return NextResponse.json(
        { error: 'Error fetching user data: ' + userDataError.message },
        { status: 500 }
      );
    }

    // @ts-ignore - 类型定义问题
    const roleName = userData?.roles?.name;
    const userRestaurantId = userData?.restaurant_id;
    const tableRestaurantId = tableData.restaurant_id;

    // 检查用户是否是超级管理员、管理员或厨房角色，以及是否属于该餐厅
    const isAdmin = roleName === 'super_admin' || roleName === 'admin';
    const isKitchen = roleName === 'kitchen';
    const isSameRestaurant = userRestaurantId === tableRestaurantId;

    if (!(isAdmin || (isKitchen && isSameRestaurant))) {
      return NextResponse.json(
        { error: 'Forbidden. User does not have permission to manage this table.' },
        { status: 403 }
      );
    }

    // 删除表格
    const { error: deleteError } = await supabase
      .from('tables')
      .delete()
      .eq('id', tableId);

    if (deleteError) {
      return NextResponse.json(
        { error: 'Error deleting table: ' + deleteError.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in tables API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
