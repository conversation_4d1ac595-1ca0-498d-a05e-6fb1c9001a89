import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const restaurantId = searchParams.get('restaurantId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const analysisType = searchParams.get('type') || 'summary'; // summary, dishes, tables, time

    if (!restaurantId) {
      return NextResponse.json(
        { error: 'Restaurant ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    
    // 基本查询 - 只查询已完成的订单
    let query = supabase
      .from('orders')
      .select(`
        *,
        order_items(
          *,
          dishes(id, name_en, name_zh, name_ja, name_ko, price)
        ),
        tables(id, table_number)
      `)
      .eq('restaurant_id', restaurantId)
      .eq('status', 'completed');
    
    // 添加日期过滤
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }
    
    const { data: orders, error } = await query;
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch orders' },
        { status: 500 }
      );
    }
    
    // 如果没有订单数据，返回空结果
    if (!orders || orders.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          totalOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0,
          analysis: {}
        }
      });
    }
    
    // 计算基本统计数据
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => {
      const orderTotal = order.order_items.reduce(
        (total, item) => total + (item.price * item.quantity),
        0
      );
      return sum + orderTotal;
    }, 0);
    const averageOrderValue = totalRevenue / totalOrders;
    
    // 根据分析类型返回不同的分析结果
    let analysisResult = {};
    
    switch (analysisType) {
      case 'dishes':
        // 分析最受欢迎的菜品
        const dishesMap = new Map();
        const dishesRevenueMap = new Map();
        
        orders.forEach(order => {
          order.order_items.forEach(item => {
            const dishId = item.dish_id;
            const dishName = item.dishes?.name_en || 'Unknown Dish';
            const quantity = item.quantity;
            const revenue = item.price * quantity;
            
            // 更新菜品数量
            if (dishesMap.has(dishId)) {
              dishesMap.set(dishId, {
                ...dishesMap.get(dishId),
                quantity: dishesMap.get(dishId).quantity + quantity
              });
            } else {
              dishesMap.set(dishId, {
                id: dishId,
                name: dishName,
                quantity: quantity
              });
            }
            
            // 更新菜品收入
            if (dishesRevenueMap.has(dishId)) {
              dishesRevenueMap.set(dishId, dishesRevenueMap.get(dishId) + revenue);
            } else {
              dishesRevenueMap.set(dishId, revenue);
            }
          });
        });
        
        // 转换为数组并排序
        const popularDishes = Array.from(dishesMap.values())
          .sort((a, b) => b.quantity - a.quantity)
          .slice(0, 10); // 取前10名
          
        const topRevenueDishes = Array.from(dishesMap.values())
          .map(dish => ({
            ...dish,
            revenue: dishesRevenueMap.get(dish.id) || 0
          }))
          .sort((a, b) => b.revenue - a.revenue)
          .slice(0, 10); // 取前10名
          
        analysisResult = {
          popularDishes,
          topRevenueDishes
        };
        break;
        
      case 'tables':
        // 分析桌位数据
        const tablesMap = new Map();
        
        orders.forEach(order => {
          const tableId = order.table_id;
          const tableNumber = order.tables?.table_number || 'Unknown Table';
          const orderTotal = order.order_items.reduce(
            (total, item) => total + (item.price * item.quantity),
            0
          );
          
          if (tablesMap.has(tableId)) {
            const tableData = tablesMap.get(tableId);
            tablesMap.set(tableId, {
              ...tableData,
              orderCount: tableData.orderCount + 1,
              totalRevenue: tableData.totalRevenue + orderTotal
            });
          } else {
            tablesMap.set(tableId, {
              id: tableId,
              tableNumber,
              orderCount: 1,
              totalRevenue: orderTotal
            });
          }
        });
        
        // 转换为数组并排序
        const tableAnalysis = Array.from(tablesMap.values())
          .sort((a, b) => b.totalRevenue - a.totalRevenue);
          
        analysisResult = {
          tableAnalysis
        };
        break;
        
      case 'time':
        // 分析时间数据
        const hourlyData = Array(24).fill(0).map((_, i) => ({
          hour: i,
          orderCount: 0,
          revenue: 0
        }));
        
        const weekdayData = Array(7).fill(0).map((_, i) => ({
          day: i,
          orderCount: 0,
          revenue: 0
        }));
        
        orders.forEach(order => {
          const orderDate = new Date(order.created_at);
          const hour = orderDate.getHours();
          const weekday = orderDate.getDay();
          const orderTotal = order.order_items.reduce(
            (total, item) => total + (item.price * item.quantity),
            0
          );
          
          // 更新小时数据
          hourlyData[hour].orderCount += 1;
          hourlyData[hour].revenue += orderTotal;
          
          // 更新星期数据
          weekdayData[weekday].orderCount += 1;
          weekdayData[weekday].revenue += orderTotal;
        });
        
        analysisResult = {
          hourlyData,
          weekdayData
        };
        break;
        
      default:
        // 默认返回摘要数据
        analysisResult = {
          recentOrders: orders.slice(0, 5), // 最近5个订单
          paymentMethods: orders.reduce((acc, order) => {
            const method = order.payment_method || 'unknown';
            acc[method] = (acc[method] || 0) + 1;
            return acc;
          }, {})
        };
        break;
    }
    
    return NextResponse.json({
      success: true,
      data: {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        analysis: analysisResult
      }
    });
  } catch (error: any) {
    console.error('Error fetching analytics data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
