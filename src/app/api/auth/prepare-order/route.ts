import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import { startOfDay, endOfDay } from 'date-fns';

export async function POST(request: NextRequest) {
  try {
    // 尝试解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { restaurantId, tableId, guestCount } = body;
    console.log('Preparing order for:', restaurantId, tableId, guestCount);

    if (!restaurantId || !tableId) {
      return NextResponse.json(
        { error: 'Restaurant ID and Table ID are required' },
        { status: 400 }
      );
    }

    // 默认为1位客人，如果未提供
    const guests = guestCount || 1;

    // 验证餐厅和桌子是否存在
    const supabase = createServiceClient();

    // 检查桌子是否存在
    const { data: table, error: tableError } = await supabase
      .from('tables')
      .select('id')
      .eq('id', tableId)
      .eq('restaurant_id', restaurantId)
      .single();

    if (tableError || !table) {
      return NextResponse.json(
        { error: 'Table not found' },
        { status: 404 }
      );
    }

    // 检查当前桌号是否有未完成的订单（当天）
    const today = new Date();
    const { data: existingOrders, error: ordersError } = await supabase
      .from('orders')
      .select('id')
      .eq('table_id', tableId)
      .eq('restaurant_id', restaurantId)
      .not('status', 'eq', 'completed')
      .gte('created_at', startOfDay(today).toISOString())
      .lte('created_at', endOfDay(today).toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (ordersError) {
      console.error('Error checking existing orders:', ordersError);
      return NextResponse.json(
        { error: 'Failed to check existing orders' },
        { status: 500 }
      );
    }

    let orderId;
    let orderIsNew = true;

    // 如果没有找到未完成的订单，创建一个新订单
    if (!existingOrders || existingOrders.length === 0) {
      const { data: newOrder, error: createOrderError } = await supabase
        .from('orders')
        .insert({
          table_id: tableId,
          restaurant_id: restaurantId,
          status: 'pending',
          guest_count: guests,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createOrderError) {
        console.error('Error creating new order:', createOrderError);
        return NextResponse.json(
          { error: 'Failed to create new order' },
          { status: 500 }
        );
      }

      orderId = newOrder.id;
    } else {
      // 使用现有订单
      orderId = existingOrders[0].id;
      orderIsNew = false;
    }

    return NextResponse.json({
      orderId, // 返回订单ID
      orderIsNew, // 返回是否为新订单
      restaurantId,
      tableId,
      guestCount: guests
    });
  } catch (error: any) {
    console.error('Error preparing order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
