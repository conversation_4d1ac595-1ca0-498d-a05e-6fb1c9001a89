import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email, language } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Default to English if no language specified
    const lang = language || 'en';

    // Create Supabase client
    const supabase = await createClient();

    // Send verification email using Supabase Auth
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        // This will redirect to our custom verification handler with language preference
        emailRedirectTo: `${process.env.NEXT_SITE_URL}/auth/verify?email=${encodeURIComponent(email)}&lang=${lang}`,
      },
    });

    if (error) {
      console.error('Error sending verification email:', error);
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error: any) {
    console.error('Error in send-verification API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
