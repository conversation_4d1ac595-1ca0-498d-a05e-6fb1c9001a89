import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // 尝试解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { token, restaurantId, tableId } = body;

    if (!token || !restaurantId || !tableId) {
      return NextResponse.json(
        { error: 'Token, Restaurant ID, and Table ID are required' },
        { status: 400 }
      );
    }

    const supabase = createServiceClient();

    // Check if the token exists and is valid
    const { data, error } = await supabase
      .from('auth_tokens')
      .select('*')
      .eq('token', token)
      .eq('restaurant_id', restaurantId)
      .eq('table_id', tableId)
      .single();

    if (error || !data) {
      return NextResponse.json(
        { valid: false, error: 'Invalid token' },
        { status: 401 }
      );
    }

    // Check if the token has expired
    const expiryTime = new Date(data.expires_at);
    const currentTime = new Date();

    if (currentTime > expiryTime) {
      return NextResponse.json(
        { valid: false, error: 'Token has expired' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      valid: true,
    });
  } catch (error: any) {
    console.error('Error verifying token:', error);
    return NextResponse.json(
      { valid: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
