import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import crypto from 'crypto';
import { startOfDay, endOfDay } from 'date-fns';

export async function POST(request: NextRequest) {
  try {
    // 尝试解析请求体
    let body;
    try {
      body = await request.json();
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { restaurantId, tableId, guestCount } = body;
    console.log(restaurantId, tableId, guestCount)
    if (!restaurantId || !tableId) {
      return NextResponse.json(
        { error: 'Restaurant ID and Table ID are required' },
        { status: 400 }
      );
    }

    // 默认为1位客人，如果未提供
    const guests = guestCount || 1;

    // Validate that the restaurant and table exist
    const supabase = createServiceClient();

    // 250515 不检查该餐厅是否存在。
    // // Check if restaurant exists
    // const { data: restaurant, error: restaurantError } = await supabase
    //   .from('restaurants')
    //   .select('id')
    //   .eq('id', restaurantId)
    //   .single();

    // if (restaurantError || !restaurant) {
    //   return NextResponse.json(
    //     { error: 'Restaurant not found' },
    //     { status: 404 }
    //   );
    // }

    // Check if table exists
    const { data: table, error: tableError } = await supabase
      .from('tables')
      .select('id')
      .eq('id', tableId)
      .eq('restaurant_id', restaurantId)
      .single();

    if (tableError || !table) {
      return NextResponse.json(
        { error: 'Table not found' },
        { status: 404 }
      );
    }

    // Generate a token
    const token = crypto.randomBytes(32).toString('hex');

    // Set expiry time (1 hour from now)
    const expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + 1);

    // 检查当前桌号是否有未完成的订单（当天）
    const today = new Date();
    const { data: existingOrders, error: ordersError } = await supabase
      .from('orders')
      .select('id')
      .eq('table_id', tableId)
      .eq('restaurant_id', restaurantId)
      .not('status', 'eq', 'completed')
      .gte('created_at', startOfDay(today).toISOString())
      .lte('created_at', endOfDay(today).toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (ordersError) {
      console.error('Error checking existing orders:', ordersError);
      return NextResponse.json(
        { error: 'Failed to check existing orders' },
        { status: 500 }
      );
    }

    let orderId;
    let orderIsNew = true;
    // 如果没有找到未完成的订单，创建一个新订单
    if (!existingOrders || existingOrders.length === 0) {
      const { data: newOrder, error: createOrderError } = await supabase
        .from('orders')
        .insert({
          table_id: tableId,
          restaurant_id: restaurantId,
          status: 'pending',
          guest_count: guests,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createOrderError) {
        console.error('Error creating new order:', createOrderError);
        return NextResponse.json(
          { error: 'Failed to create new order' },
          { status: 500 }
        );
      }

      orderId = newOrder.id;
    } else {
      // 使用现有订单
      orderId = existingOrders[0].id;
      orderIsNew = false;
    }

    // Store the token in the database
    const { error: insertError } = await supabase
      .from('auth_tokens')
      .insert({
        token,
        restaurant_id: restaurantId,
        table_id: tableId,
        order_id: orderId, // 存储关联的订单ID
        expires_at: expiryTime.toISOString(),
      });

    if (insertError) {
      console.error('Error storing token:', insertError);
      return NextResponse.json(
        { error: 'Failed to generate token' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      token,
      expiry: expiryTime.toISOString(),
      orderId, // 返回订单ID
      orderIsNew, // 返回是否为新订单
    });
  } catch (error: any) {
    console.error('Error generating token:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
