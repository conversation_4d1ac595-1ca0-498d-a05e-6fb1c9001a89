import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { restaurantName, email, password } = body;

    if (!restaurantName || !email || !password) {
      return NextResponse.json(
        { error: 'Restaurant name, email, and password are required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    const adminClient = await createAdminClient();

    // Check if we've reached the limit of 10 free restaurants
    const { count, error: countError } = await supabase
      .from('restaurants')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting restaurants:', countError);
      return NextResponse.json(
        { error: 'Failed to check restaurant count' },
        { status: 500 }
      );
    }

    const isFreeEligible = count !== null && count < 10;

    // Get the basic service plan ID
    const { data: basicPlanData, error: planError } = await supabase
      .from('service_plans')
      .select('id')
      .eq('name', 'basic')
      .single();

    if (planError) {
      console.error('Error fetching basic service plan:', planError);
      return NextResponse.json(
        { error: 'Failed to fetch service plan information' },
        { status: 500 }
      );
    }

    // Register the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 });
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Create restaurant record
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .insert({
        name: restaurantName,
        is_active: true,
      })
      .select()
      .single();

    if (restaurantError) {
      // Rollback: Delete the auth user if restaurant creation fails
      await adminClient.auth.admin.deleteUser(authData.user.id);

      return NextResponse.json(
        { error: 'Failed to create restaurant' },
        { status: 500 }
      );
    }

    // Calculate service end date (3 months from now for free trial, or 1 month for regular)
    const serviceEndDate = new Date();
    if (isFreeEligible) {
      // 90 days free trial
      serviceEndDate.setDate(serviceEndDate.getDate() + 90);
    } else {
      // 30 days regular trial
      serviceEndDate.setDate(serviceEndDate.getDate() + 30);
    }

    // Create restaurant service record
    const { error: serviceError } = await supabase
      .from('restaurant_services')
      .insert({
        restaurant_id: restaurantData.id,
        service_plan_id: basicPlanData.id,
        start_date: new Date().toISOString(),
        end_date: serviceEndDate.toISOString(),
        is_trial: true
      });

    if (serviceError) {
      // Rollback: Delete the restaurant and auth user if service creation fails
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      await adminClient.auth.admin.deleteUser(authData.user.id);

      return NextResponse.json(
        { error: 'Failed to create service subscription' },
        { status: 500 }
      );
    }

    // Get the restaurant owner role ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'restaurant_owner')
      .single();

    if (roleError) {
      // Rollback: Delete the auth user and restaurant if role fetch fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      await supabase
        .from('restaurants')
        .delete()
        .eq('id', restaurantData.id);

      return NextResponse.json(
        { error: 'Failed to fetch role information' },
        { status: 500 }
      );
    }

    // Create user record with role and restaurant association
    const { error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        email: email,
        role_id: roleData.id,
        restaurant_id: restaurantData.id,
        is_active: true,
      });

    if (userError) {
      // Rollback: Delete the auth user and restaurant if user creation fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      await supabase
        .from('restaurants')
        .delete()
        .eq('id', restaurantData.id);

      return NextResponse.json(
        { error: 'Failed to create user record' },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Registration successful',
      restaurant: {
        id: restaurantData.id,
        name: restaurantName
      },
      service: {
        plan: 'basic',
        isTrial: true,
        endDate: serviceEndDate.toISOString(),
        isFreeEligible
      }
    });
  } catch (error: any) {
    console.error('Error in registration API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
