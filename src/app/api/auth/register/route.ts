import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { restaurantName, email, password } = body;

    if (!restaurantName || !email || !password) {
      return NextResponse.json(
        { error: 'Restaurant name, email, and password are required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();
    const adminClient = await createAdminClient();

    // Check if we've reached the limit of 10 free restaurants
    const { count, error: countError } = await supabase
      .from('restaurants')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting restaurants:', countError);
      return NextResponse.json(
        { error: 'Failed to check restaurant count' },
        { status: 500 }
      );
    }

    const isFreeEligible = count !== null && count < 10;

    // Register the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 });
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    // Create restaurant record
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .insert({
        name: restaurantName,
        is_active: true,
        free_trial: isFreeEligible,
        free_trial_end_date: isFreeEligible 
          ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() // 90 days from now
          : null,
      })
      .select()
      .single();

    if (restaurantError) {
      // Rollback: Delete the auth user if restaurant creation fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { error: 'Failed to create restaurant' },
        { status: 500 }
      );
    }

    // Get the restaurant owner role ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'restaurant_owner')
      .single();

    if (roleError) {
      // Rollback: Delete the auth user and restaurant if role fetch fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      await supabase
        .from('restaurants')
        .delete()
        .eq('id', restaurantData.id);
      
      return NextResponse.json(
        { error: 'Failed to fetch role information' },
        { status: 500 }
      );
    }

    // Create user record with role and restaurant association
    const { error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        email: email,
        role_id: roleData.id,
        restaurant_id: restaurantData.id,
        is_active: true,
      });

    if (userError) {
      // Rollback: Delete the auth user and restaurant if user creation fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      await supabase
        .from('restaurants')
        .delete()
        .eq('id', restaurantData.id);
      
      return NextResponse.json(
        { error: 'Failed to create user record' },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Registration successful',
      isFreeEligible,
    });
  } catch (error: any) {
    console.error('Error in registration API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
