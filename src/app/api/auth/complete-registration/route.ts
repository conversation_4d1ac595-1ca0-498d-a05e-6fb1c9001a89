import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createAdminClient } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const { restaurantName, password, email } = await request.json();

    if (!restaurantName || !password || !email) {
      return NextResponse.json(
        { error: 'Restaurant name, password, and email are required' },
        { status: 400 }
      );
    }

    // Create Supabase clients
    const supabase = await createClient();
    const adminClient = await createAdminClient();

    // Check if we've reached the limit of 10 free restaurants
    const { count, error: countError } = await supabase
      .from('restaurants')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting restaurants:', countError);
      return NextResponse.json(
        { error: 'Failed to check restaurant count' },
        { status: 500 }
      );
    }

    const isFreeEligible = count !== null && count < 10;
    
    // Get the basic service plan ID
    const { data: basicPlanData, error: planError } = await supabase
      .from('service_plans')
      .select('id')
      .eq('name', 'basic')
      .single();
      
    if (planError) {
      console.error('Error fetching basic service plan:', planError);
      return NextResponse.json(
        { error: 'Failed to fetch service plan information' },
        { status: 500 }
      );
    }

    // Create the user with email and password using admin client
    const { data: authData, error: authError } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Email is already verified
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return NextResponse.json(
        { error: 'Failed to create user account' },
        { status: 500 }
      );
    }

    // Create restaurant record
    const { data: restaurantData, error: restaurantError } = await supabase
      .from('restaurants')
      .insert({
        name: restaurantName,
        is_active: true,
      })
      .select()
      .single();

    if (restaurantError) {
      // Rollback: Delete the auth user if restaurant creation fails
      await adminClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { error: 'Failed to create restaurant' },
        { status: 500 }
      );
    }
    
    // Calculate service end date
    // For first 10 restaurants: no end date (permanent)
    // For others: 3 months free trial
    const serviceEndDate = new Date();
    if (!isFreeEligible) {
      // 90 days free trial for regular users
      serviceEndDate.setDate(serviceEndDate.getDate() + 90);
    } else {
      // For first 10 restaurants, set a very far future date (effectively permanent)
      serviceEndDate.setFullYear(serviceEndDate.getFullYear() + 100);
    }
    
    // Create restaurant service record
    const { error: serviceError } = await supabase
      .from('restaurant_services')
      .insert({
        restaurant_id: restaurantData.id,
        service_plan_id: basicPlanData.id,
        start_date: new Date().toISOString(),
        end_date: serviceEndDate.toISOString(),
        is_trial: true
      });
      
    if (serviceError) {
      // Rollback: Delete the restaurant and auth user if service creation fails
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      await adminClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { error: 'Failed to create service subscription' },
        { status: 500 }
      );
    }

    // Get the restaurant owner role ID
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'restaurant_owner')
      .single();

    if (roleError) {
      // Rollback: Delete everything if role fetch fails
      await supabase.from('restaurant_services').delete().eq('restaurant_id', restaurantData.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      await adminClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { error: 'Failed to fetch user role' },
        { status: 500 }
      );
    }

    // Create user record in our users table
    const { error: userError } = await supabase
      .from('users')
      .insert({
        auth_id: authData.user.id,
        email: email,
        name: `${restaurantName} Owner`,
        role_id: roleData.id,
        restaurant_id: restaurantData.id,
      });

    if (userError) {
      // Rollback: Delete everything if user creation fails
      await supabase.from('restaurant_services').delete().eq('restaurant_id', restaurantData.id);
      await supabase.from('restaurants').delete().eq('id', restaurantData.id);
      await adminClient.auth.admin.deleteUser(authData.user.id);
      
      return NextResponse.json(
        { error: 'Failed to create user record' },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Registration completed successfully',
      restaurant: {
        id: restaurantData.id,
        name: restaurantName
      },
      service: {
        plan: 'basic',
        isTrial: true,
        endDate: serviceEndDate.toISOString(),
        isFreeEligible
      }
    });
  } catch (error: any) {
    console.error('Error in complete-registration API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
