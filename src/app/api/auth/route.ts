import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 登录
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 401 });
    }

    if (!data.user) {
      return NextResponse.json({ error: 'Login failed' }, { status: 401 });
    }

    // 获取用户角色和餐厅 ID
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role_id, restaurant_id, roles:role_id(name)')
      .eq('auth_id', data.user.id)
      .single();

    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 500 });
    }

    // 返回用户信息和会话
    return NextResponse.json({
      user: data.user,
      session: data.session,
      userData,
    });
  } catch (error: any) {
    console.error('Error in auth API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. User not authenticated.' },
        { status: 401 }
      );
    }

    // 获取用户角色和餐厅 ID
    const { data: userData, error: userDataError } = await supabase
      .from('users')
      .select('role_id, restaurant_id, roles:role_id(name)')
      .eq('auth_id', user.id)
      .single();

    if (userDataError) {
      return NextResponse.json({ error: userDataError.message }, { status: 500 });
    }

    // 返回用户信息
    return NextResponse.json({
      user,
      userData,
    });
  } catch (error: any) {
    console.error('Error in auth API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 登出
    const { error } = await supabase.auth.signOut();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // 清除 cookie
    const c = await cookies();
    c.delete('sb-auth-token');

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error in auth API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
