import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * 刷新会话 API
 */
export async function POST(request: NextRequest) {
  try {
    // 创建 Supabase 客户端
    const supabase = await createClient();

    // 刷新会话
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('Error refreshing session:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, session: data.session });
  } catch (error: any) {
    console.error('Error in refresh session API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
