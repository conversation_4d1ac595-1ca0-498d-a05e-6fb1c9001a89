import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { token_hash, type } = await request.json();

    if (!token_hash || !type) {
      return NextResponse.json(
        { error: 'Token hash and type are required' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = await createClient();

    // Verify the email using the token
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash,
      type: type as any,
    });

    if (error) {
      console.error('Email verification error:', error);
      return NextResponse.json(
        { error: error.message || 'Email verification failed' },
        { status: 400 }
      );
    }

    // Return success with user email
    return NextResponse.json({
      success: true,
      message: 'Email verified successfully',
      email: data.user?.email,
    });
  } catch (error: any) {
    console.error('Error in verify API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
