import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/server';
import { broadcastToRestaurant } from '@/app/api/kitchen/sse/route';

/**
 * 客户呼叫店员API
 */
export async function POST(request: NextRequest) {
  try {
    const { token, tableId } = await request.json();

    if (!token || !tableId) {
      return NextResponse.json({ 
        error: 'Token and table ID are required' 
      }, { status: 400 });
    }

    // 验证token
    const supabase = createServiceClient();
    const { data: tokenData, error: tokenError } = await supabase
      .from('auth_tokens')
      .select('order_id, restaurant_id, table_id, expires_at')
      .eq('token', token)
      .single();

    if (tokenError || !tokenData) {
      console.error('Token validation error:', tokenError);
      return NextResponse.json({ 
        error: 'Invalid or expired session' 
      }, { status: 401 });
    }

    // 检查token是否过期
    const expiryTime = new Date(tokenData.expires_at);
    const currentTime = new Date();
    if (currentTime > expiryTime) {
      return NextResponse.json({ 
        error: 'Session has expired' 
      }, { status: 401 });
    }

    // 验证桌号匹配
    if (tokenData.table_id !== tableId) {
      return NextResponse.json({ 
        error: 'Table ID mismatch' 
      }, { status: 401 });
    }

    // 获取餐桌信息
    const { data: tableData, error: tableError } = await supabase
      .from('tables')
      .select('table_number')
      .eq('id', tableId)
      .single();

    if (tableError || !tableData) {
      console.error('Table query error:', tableError);
      return NextResponse.json({ 
        error: 'Table not found' 
      }, { status: 404 });
    }

    console.log(`🔔 Customer calling staff from table ${tableData.table_number}`);

    // 通过SSE通知厨房有客户呼叫
    broadcastToRestaurant(tokenData.restaurant_id, 'staff_call', {
      tableId: tableId,
      tableNumber: tableData.table_number,
      timestamp: new Date().toISOString(),
      message: `Table ${tableData.table_number} is calling for staff`
    });

    console.log(`📤 Staff call notification sent for table ${tableData.table_number} to restaurant: ${tokenData.restaurant_id}`);

    return NextResponse.json({ 
      success: true,
      tableNumber: tableData.table_number,
      message: 'Staff call sent successfully'
    });

  } catch (error: any) {
    console.error('Error in staff call API:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
