import { Suspense } from 'react';
import AdminDashboard from '@/components/admin/AdminDashboard';
import AdminLayout from '@/components/admin/AdminLayout';
import Loading from '@/components/common/Loading';

export default function AdminPage() {
  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <Suspense fallback={<Loading />}>
          <AdminDashboard />
        </Suspense>
      </div>
    </AdminLayout>
  );
}
