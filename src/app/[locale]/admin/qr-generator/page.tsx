'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { apiGet } from '@/lib/api';
import { QRCodeSVG } from 'qrcode.react';
import AdminLayout from '@/components/admin/AdminLayout';

type Restaurant = {
  id: string;
  name: string;
};

type Table = {
  id: string;
  table_number: string;
  restaurant_id: string;
};

export default function QRGeneratorPage() {
  const t = useTranslations();
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [tables, setTables] = useState<Table[]>([]);
  const [selectedRestaurant, setSelectedRestaurant] = useState<string>('');
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [qrValue, setQrValue] = useState<string>('');
  const [baseUrl, setBaseUrl] = useState<string>('');

  useEffect(() => {
    // Get the base URL of the application
    setBaseUrl(window.location.origin);

    // Fetch restaurants
    fetchRestaurants();
  }, []);

  useEffect(() => {
    if (selectedRestaurant) {
      fetchTables(selectedRestaurant);
    } else {
      setTables([]);
      setSelectedTable('');
    }
  }, [selectedRestaurant]);

  useEffect(() => {
    if (selectedRestaurant && selectedTable && baseUrl) {
      // Generate QR code URL - without language parameter
      const url = `${baseUrl}/auth/${selectedRestaurant}/${selectedTable}`;
      setQrValue(url);
    } else {
      setQrValue('');
    }
  }, [selectedRestaurant, selectedTable, baseUrl]);

  const fetchRestaurants = async () => {
    setLoading(true);
    try {
      const data = await apiGet<Restaurant[]>('/restaurants/all');
      setRestaurants(data || []);
      if (data && data.length > 0) {
        setSelectedRestaurant(data[0].id);
      }
    } catch (error: any) {
      console.error('Error fetching restaurants:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTables = async (restaurantId: string) => {
    setLoading(true);
    try {
      const data = await apiGet<Table[]>(`/tables?restaurantId=${restaurantId}`);
      setTables(data || []);
      if (data && data.length > 0) {
        setSelectedTable(data[0].id);
      } else {
        setSelectedTable('');
      }
    } catch (error: any) {
      console.error('Error fetching tables:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRestaurantChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedRestaurant(e.target.value);
  };

  const handleTableChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTable(e.target.value);
  };

  const downloadQRCode = () => {
    const canvas = document.getElementById('qr-code') as HTMLCanvasElement;
    if (canvas) {
      const pngUrl = canvas
        .toDataURL('image/png')
        .replace('image/png', 'image/octet-stream');

      const downloadLink = document.createElement('a');
      downloadLink.href = pngUrl;
      downloadLink.download = `qr-restaurant-${selectedRestaurant}-table-${selectedTable}.png`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  };

  return (
    <AdminLayout>
      <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">QR Code Generator</h1>
      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <p className="text-sm text-blue-700">
          <strong>Note:</strong> The QR codes generated here are permanent and can be printed for long-term use.
          When a customer scans the QR code, they will receive a temporary authorization token valid for 1 hour.
          This security measure ensures that only customers physically present in the restaurant can place orders.
        </p>
        <p className="text-sm text-blue-700 mt-2">
          The system will automatically detect the customer's preferred language based on their browser settings.
          Customers can also manually change the language once they reach the ordering page.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <div className="mb-4">
            <label htmlFor="restaurant" className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.restaurant')}
            </label>
            <select
              id="restaurant"
              value={selectedRestaurant}
              onChange={handleRestaurantChange}
              className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a restaurant</option>
              {restaurants.map(restaurant => (
                <option key={restaurant.id} value={restaurant.id}>
                  {restaurant.name}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label htmlFor="table" className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.table')}
            </label>
            <select
              id="table"
              value={selectedTable}
              onChange={handleTableChange}
              disabled={!selectedRestaurant}
              className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a table</option>
              {tables.map(table => (
                <option key={table.id} value={table.id}>
                  {table.table_number}
                </option>
              ))}
            </select>
          </div>

          {qrValue && (
            <div className="mt-6">
              <p className="text-sm text-gray-600 mb-2">QR Code URL:</p>
              <div className="bg-gray-100 p-3 rounded-md break-all text-xs">
                {qrValue}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col items-center justify-center">
          {qrValue ? (
            <>
              <div className="bg-white p-4 rounded-lg shadow-md">
                <QRCodeSVG
                  id="qr-code"
                  value={qrValue}
                  size={200}
                  level="H"
                />
              </div>
              <button
                onClick={downloadQRCode}
                className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md"
              >
                Download QR Code
              </button>
            </>
          ) : (
            <div className="text-center text-gray-500">
              <p>Select a restaurant and table to generate a QR code</p>
            </div>
          )}
        </div>
      </div>
    </div>
    </AdminLayout>
  );
}
