'use client';

import { Suspense, useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import CustomerMenu from '@/components/customer/CustomerMenu';
import CustomerCart from '@/components/customer/CustomerCart';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Loading from '@/components/common/Loading';
import { useCartStore } from '@/lib/stores/cartStore';
import CallStaffButton from '@/components/customer/CallStaffButton';

export default function CustomerOrderingClientPage({
  tableId,
}: {
  tableId: string;
}) {
  const t = useTranslations();
  const { items } = useCartStore();
  const [cartItemsCount, setCartItemsCount] = useState(0);
  const [isMobileCartVisible, setIsMobileCartVisible] = useState(false);
  const [tableNumber, setTableNumber] = useState<string>('');

  // 获取编号
  useEffect(() => {
    const fetchTableNumber = async () => {
      try {
        // 使用 API 路由获取餐桌信息
        const response = await fetch(`/api/tables?id=${tableId}`);

        if (!response.ok) {
          throw new Error(`Error fetching table: ${response.statusText}`);
        }

        const res = await response.json();
        console.log(res.data)
        if (res?.data?.table_number) {
          setTableNumber(res.data.table_number);
        }
      } catch (error: any) {
        console.error('Error fetching table number:', error);
      }
    };

    fetchTableNumber();
  }, [tableId]);

  // 计算购物车中的未提交商品数量
  useEffect(() => {
    const tableItems = items.filter(item => item.tableId === tableId && !item.submitted);
    const count = tableItems.reduce((total, item) => total + item.quantity, 0);
    setCartItemsCount(count);
  }, [items, tableId]);
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          {/* <h1 className="text-xl font-semibold text-gray-900">
            {tableNumber || '...'}
          </h1> */}
          <CallStaffButton tableId={tableId} />
        </div>
        <div className="flex items-center space-x-4">
          {/* 购物车图标 - 移动端点击显示购物车面板 */}
          <div className="relative cursor-pointer" onClick={() => setIsMobileCartVisible(!isMobileCartVisible)}>
            <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            {cartItemsCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {cartItemsCount}
              </span>
            )}
          </div>
          <LanguageSwitcher />
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="lg:w-2/3">
            <Suspense fallback={<Loading />}>
              <CustomerMenu tableId={tableId} />
            </Suspense>
          </div>

          {/* 桌面端购物车（始终显示） */}
          <div className="hidden lg:block lg:w-1/3">
            <Suspense fallback={<Loading />}>
              <CustomerCart tableId={tableId} />
            </Suspense>
          </div>

          {/* 移动端购物车（点击图标显示/隐藏） */}
          {isMobileCartVisible && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden">
              <div className="absolute right-0 top-0 h-full w-4/5 max-w-md bg-white overflow-auto">
                <div className="p-4 flex justify-between items-center border-b">
                  <h2 className="text-lg font-semibold">{t('common.cart')}</h2>
                  <button onClick={() => setIsMobileCartVisible(false)} className="text-gray-500">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <div className="p-4">
                  <Suspense fallback={<Loading />}>
                    <CustomerCart tableId={tableId} />
                  </Suspense>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
