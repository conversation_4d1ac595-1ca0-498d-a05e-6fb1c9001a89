'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Register - Kyoto Kitchen QR',
  description: 'Register your restaurant for the Kyoto Kitchen QR ordering system. First 10 restaurants get 3 months free!',
  keywords: 'restaurant registration, QR code ordering signup, digital menu registration, contactless ordering registration',
};

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function RegisterPage() {
  const t = useTranslations('auth');
  const router = useRouter();
  const locale = useLocale();

  const [restaurantName, setRestaurantName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!restaurantName || !email || !password) {
      setError(t('emailPasswordRequired'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('passwordsDontMatch'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the API to register the user
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restaurantName,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      // Show success message
      setSuccess(true);

      // Redirect to login page after a delay
      setTimeout(() => {
        router.push(`/${locale}/login`);
      }, 3000);
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || t('registerFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600">
              Kyoto Kitchen QR
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
              Pricing
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Registration Form */}
      <motion.div
        className="max-w-md mx-auto my-16 bg-white p-8 rounded-lg shadow-md"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">{t('register')}</h2>
          <p className="text-gray-600 mt-2">
            {t('freeTrial')}
          </p>
        </div>

        {success ? (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
            <span className="block sm:inline">{t('registerSuccess')}</span>
          </div>
        ) : (
          <form onSubmit={handleRegister}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                <span className="block sm:inline">{error}</span>
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="restaurantName" className="block text-gray-700 text-sm font-bold mb-2">
                {t('restaurantName')}
              </label>
              <input
                id="restaurantName"
                type="text"
                value={restaurantName}
                onChange={(e) => setRestaurantName(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
                {t('email')}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
                {t('password')}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="confirmPassword" className="block text-gray-700 text-sm font-bold mb-2">
                {t('confirmPassword')}
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                disabled={loading}
              >
                {loading ? t('registering') : t('register')}
              </button>
            </div>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                {t('alreadyHaveAccount')}{' '}
                <Link href={`/${locale}/login`} className="text-blue-600 hover:text-blue-800">
                  {t('signIn')}
                </Link>
              </p>
            </div>
          </form>
        )}
      </motion.div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-auto">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Kyoto Kitchen QR. All rights reserved.</p>
          <p className="mt-2">Email: <EMAIL></p>
        </div>
      </footer>
    </div>
  );
}
