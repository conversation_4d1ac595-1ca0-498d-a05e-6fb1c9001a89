'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import PublicLayout from '@/components/layout/PublicLayout';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function RegisterPage() {
  const t = useTranslations('auth');
  const locale = useLocale();

  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!email) {
      setError(t('emailRequired'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the API to send verification email
      const response = await fetch('/api/auth/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          language: locale, // Send the current language preference
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send verification email');
      }

      // Show success message
      setEmailSent(true);
    } catch (error: any) {
      console.error('Email verification error:', error);
      setError(error.message || t('emailVerificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <PublicLayout
      title="Register - OISII.LIFE"
      description="Register your restaurant for the Oisii ordering system. First 10 restaurants use for FREE forever!"
      keywords="restaurant registration, QR code ordering signup, digital menu registration, contactless ordering registration"
      footerVariant="simple"
    >

      {/* Registration Form */}
      <motion.div
        className="max-w-md mx-auto my-16 bg-white p-8 rounded-lg shadow-md"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">{t('register')}</h2>
          <p className="text-gray-600 mt-2">
            {t('joinPlatform')}
          </p>
        </div>

        {emailSent ? (
          <div className="text-center">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
              <span className="block sm:inline">{t('emailVerificationSent')}</span>
            </div>
            <p className="text-gray-600 mb-4">{t('checkEmailInstructions')}</p>
            <button
              onClick={() => setEmailSent(false)}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {t('resendEmail')}
            </button>
          </div>
        ) : (
          <form onSubmit={handleEmailSubmit}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                <span className="block sm:inline">{error}</span>
              </div>
            )}

            <div className="mb-6">
              <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
                {t('email')}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder={t('enterEmailPlaceholder')}
              />
            </div>

            <div className="flex items-center justify-between">
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                disabled={loading}
              >
                {loading ? t('sendingEmail') : t('sendVerificationEmail')}
              </button>
            </div>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                {t('alreadyHaveAccount')}{' '}
                <Link href={`/${locale}/login`} className="text-blue-600 hover:text-blue-800">
                  {t('signIn')}
                </Link>
              </p>
            </div>
          </form>
        )}
      </motion.div>

    </PublicLayout>
  );
}
