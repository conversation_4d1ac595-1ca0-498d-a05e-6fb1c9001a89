'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Head from 'next/head';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function RegisterPage() {
  const t = useTranslations('auth');
  const router = useRouter();
  const locale = useLocale();

  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!email) {
      setError(t('emailRequired'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the API to send verification email
      const response = await fetch('/api/auth/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send verification email');
      }

      // Show success message
      setEmailSent(true);
    } catch (error: any) {
      console.error('Email verification error:', error);
      setError(error.message || t('emailVerificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <Head>
        <link rel="icon" href="/favicon.ico" />
        <title>Register - OISII.LIFE</title>
        <meta name="description" content="Register your restaurant for the Oisii ordering system. First 10 restaurants use for FREE forever!" />
        <meta name="keywords" content="restaurant registration, QR code ordering signup, digital menu registration, contactless ordering registration" />
      </Head>
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Image
              src="/logo.png"
              alt="oisii.life"
              width={60}
              height={60}
              />
            <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600">
              OISII
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
              Pricing
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Registration Form */}
      <motion.div
        className="max-w-md mx-auto my-16 bg-white p-8 rounded-lg shadow-md"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">{t('register')}</h2>
          <p className="text-gray-600 mt-2">
            {t('joinPlatform')}
          </p>
        </div>

        {emailSent ? (
          <div className="text-center">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
              <span className="block sm:inline">{t('emailVerificationSent')}</span>
            </div>
            <p className="text-gray-600 mb-4">{t('checkEmailInstructions')}</p>
            <button
              onClick={() => setEmailSent(false)}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {t('resendEmail')}
            </button>
          </div>
        ) : (
          <form onSubmit={handleEmailSubmit}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                <span className="block sm:inline">{error}</span>
              </div>
            )}

            <div className="mb-6">
              <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
                {t('email')}
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder={t('enterEmailPlaceholder')}
              />
            </div>

            <div className="flex items-center justify-between">
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                disabled={loading}
              >
                {loading ? t('sendingEmail') : t('sendVerificationEmail')}
              </button>
            </div>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                {t('alreadyHaveAccount')}{' '}
                <Link href={`/${locale}/login`} className="text-blue-600 hover:text-blue-800">
                  {t('signIn')}
                </Link>
              </p>
            </div>
          </form>
        )}
      </motion.div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-auto">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Oisii. All rights reserved.</p>
          <p className="mt-2">Email: <EMAIL></p>
        </div>
      </footer>
    </div>
  );
}
