'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Head from 'next/head';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function CompleteRegistrationPage() {
  const t = useTranslations('auth');
  const router = useRouter();
  const locale = useLocale();
  const searchParams = useSearchParams();

  const [restaurantName, setRestaurantName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [email, setEmail] = useState('');

  useEffect(() => {
    // Get email from URL parameters
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
      setVerifying(false);
    } else {
      // If no email parameter, redirect to register page
      router.push(`/${locale}/register`);
    }
  }, [searchParams, router, locale]);

  const handleCompleteRegistration = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!restaurantName || !password) {
      setError(t('emailPasswordRequired'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('passwordsDontMatch'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the API to complete registration
      const response = await fetch('/api/auth/complete-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restaurantName,
          password,
          email,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      // Show success message
      setSuccess(true);

      // Redirect to login page after a delay
      setTimeout(() => {
        router.push(`/${locale}/login`);
      }, 3000);
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message || t('registerFailed'));
    } finally {
      setLoading(false);
    }
  };

  if (verifying) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Verifying email...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <Head>
        <title>Complete Registration - Oisii</title>
        <meta name="description" content="Complete your restaurant registration for the Oisii ordering system." />
        <meta name="keywords" content="restaurant registration, QR code ordering signup, digital menu registration" />
      </Head>
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600">
              Oisii
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
              Pricing
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Complete Registration Form */}
      <motion.div
        className="max-w-md mx-auto my-16 bg-white p-8 rounded-lg shadow-md"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800">{t('completeRegistration')}</h2>
          <p className="text-gray-600 mt-2">
            {t('emailVerified')} {email}
          </p>
        </div>

        {success ? (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
            <span className="block sm:inline">{t('registerSuccess')}</span>
          </div>
        ) : (
          <form onSubmit={handleCompleteRegistration}>
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
                <span className="block sm:inline">{error}</span>
              </div>
            )}

            <div className="mb-4">
              <label htmlFor="restaurantName" className="block text-gray-700 text-sm font-bold mb-2">
                {t('restaurantName')}
              </label>
              <input
                id="restaurantName"
                type="text"
                value={restaurantName}
                onChange={(e) => setRestaurantName(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder={t('enterRestaurantName')}
              />
            </div>

            <div className="mb-4">
              <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
                {t('password')}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder={t('enterPassword')}
              />
            </div>

            <div className="mb-6">
              <label htmlFor="confirmPassword" className="block text-gray-700 text-sm font-bold mb-2">
                {t('confirmPassword')}
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder={t('confirmPasswordPlaceholder')}
              />
            </div>

            <div className="flex items-center justify-between">
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                disabled={loading}
              >
                {loading ? t('registering') : t('completeRegistration')}
              </button>
            </div>
          </form>
        )}
      </motion.div>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8 mt-auto">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Oisii. All rights reserved.</p>
          <p className="mt-2">Email: <EMAIL></p>
        </div>
      </footer>
    </div>
  );
}
