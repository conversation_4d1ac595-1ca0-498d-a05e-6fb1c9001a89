/**
 * Test page for admin i18n functionality
 * This page demonstrates that admin translations are properly loaded
 */

'use client';

import { useTranslations } from 'next-intl';
import { useLocale } from 'next-intl';
import AdminTranslationExample from '@/components/admin/AdminTranslationExample';

export default function TestAdminI18nPage() {
  const locale = useLocale();
  const admin = useTranslations('admin');
  const landing = useTranslations('landing');

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            Admin i18n Test Page
          </h1>
          
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Translation Test Results</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-lg mb-3 text-blue-600">Admin Translations</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>admin.title:</strong> {admin('title')}</div>
                  <div><strong>admin.analytics.title:</strong> {admin('analytics.title')}</div>
                  <div><strong>admin.analytics.selectRestaurant:</strong> {admin('analytics.selectRestaurant')}</div>
                  <div><strong>admin.analytics.startDate:</strong> {admin('analytics.startDate')}</div>
                  <div><strong>admin.analytics.endDate:</strong> {admin('analytics.endDate')}</div>
                  <div><strong>admin.analytics.analysisType:</strong> {admin('analytics.analysisType')}</div>
                  <div><strong>admin.analytics.summary:</strong> {admin('analytics.summary')}</div>
                  <div><strong>admin.analytics.dishes:</strong> {admin('analytics.dishes')}</div>
                  <div><strong>admin.analytics.tables:</strong> {admin('analytics.tables')}</div>
                  <div><strong>admin.analytics.time:</strong> {admin('analytics.time')}</div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-lg mb-3 text-green-600">Regular Translations</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>landing.title:</strong> {landing('title')}</div>
                  <div><strong>landing.home:</strong> {landing('home')}</div>
                  <div><strong>landing.login:</strong> {landing('login')}</div>
                  <div><strong>landing.contactEmail:</strong> {landing('contactEmail')}</div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-100 rounded">
              <h4 className="font-semibold mb-2">Configuration Info</h4>
              <div className="text-sm space-y-1">
                <div><strong>Current Locale:</strong> {locale}</div>
                <div><strong>Translation Loading:</strong> Merged from both directories</div>
                <div><strong>Admin Source:</strong> src/i18n/admin/{locale}.json</div>
                <div><strong>Regular Source:</strong> src/i18n/messages/{locale}.json</div>
              </div>
            </div>
          </div>
          
          <AdminTranslationExample locale={locale} />
        </div>
      </div>
    </div>
  );
}
