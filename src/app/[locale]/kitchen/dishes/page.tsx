'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useRestaurantStore } from '@/store/restaurantStore';
import Loading from '@/components/common/Loading';
import KitchenLayout from '@/components/kitchen/KitchenLayout';
import Image from 'next/image';
import ImageUploader from '@/components/common/ImageUploader';
import { getCategoryName } from '@/lib/i18n-utils';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// 模态框组件
function Modal({ isOpen, onClose, title, children }: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      {/* 模态框内容 */}
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-1"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}

interface Dish {
  id: string;
  name_zh: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  description_zh: string;
  description_en: string;
  description_ja: string;
  description_ko: string;
  price: number;
  category_id: string;
  restaurant_id: string;
  sort_order: number;
  image_url: string | null;
}

interface Category {
  id: string;
  name_zh: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  restaurant_id: string;
  sort_order: number;
}

// 可排序的表格行组件
function SortableTableRow({
  dish,
  onEdit,
  onDelete,
  tCommon,
  categories,
  locale
}: {
  dish: Dish;
  onEdit: (dish: Dish) => void;
  onDelete: (id: string) => void;
  tCommon: any;
  categories: Category[];
  locale: string;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: dish.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
    position: 'relative' as const,
    cursor: 'grab'
  };

  // 获取分类名称（根据当前语言）
  const getLocalizedCategoryName = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    if (!category) return 'Unknown Category';
    return getCategoryName(category, locale);
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    return price.toFixed(2);
  };

  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center">
        <svg className="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
        </svg>
        {dish.sort_order}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {dish.name_zh}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {dish.name_ja}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {dish.name_en}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {dish.name_ko}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {getLocalizedCategoryName(dish.category_id)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        ¥{formatPrice(dish.price)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button
          onClick={() => onEdit(dish)}
          className="text-indigo-600 hover:text-indigo-900 mr-3"
        >
          {tCommon('edit')}
        </button>
        <button
          onClick={() => onDelete(dish.id)}
          className="text-red-600 hover:text-red-900"
        >
          {tCommon('delete')}
        </button>
      </td>
    </tr>
  );
}

export default function DishesPage() {
  const t = useTranslations('kitchen');
  const tCommon = useTranslations('common');
  const locale = useLocale();
  const restaurantStore = useRestaurantStore();
  const restaurantId = restaurantStore.getRestaurantId();

  // 客户端渲染标志
  const [isClient, setIsClient] = useState(false);

  // 菜品列表和表单状态
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 编辑状态
  const [editingId, setEditingId] = useState<string | null>(null);

  // 模态框状态
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 表单状态
  const [dishName, setDishName] = useState('');
  const [dishDescription, setDishDescription] = useState('');
  const [dishPriceStr, setDishPriceStr] = useState<string>('');
  const [dishCategory, setDishCategory] = useState<string>('');
  const [dishImage, setDishImage] = useState<string>('');
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [inputLanguage, setInputLanguage] = useState<'zh' | 'ja'>('zh'); // 默认中文输入

  // 拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 在组件挂载后设置客户端渲染标志
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 获取菜品和分类列表
  useEffect(() => {
    if (!restaurantId) return;

    const fetchData = async () => {
      try {
        // 获取菜品列表
        const dishesResponse = await fetch(`/api/dishes?restaurantId=${restaurantId}`);
        if (!dishesResponse.ok) {
          throw new Error('Failed to fetch dishes');
        }

        const dishesResult = await dishesResponse.json();
        if (dishesResult.data && Array.isArray(dishesResult.data)) {
          // 按排序字段排序
          const sortedDishes = [...dishesResult.data].sort((a, b) => a.sort_order - b.sort_order);
          setDishes(sortedDishes);
        } else {
          setDishes([]);
          console.error('Dishes data is not an array:', dishesResult);
        }

        // 获取分类列表
        const categoriesResponse = await fetch(`/api/categories?restaurantId=${restaurantId}`);
        if (!categoriesResponse.ok) {
          throw new Error('Failed to fetch categories');
        }

        const categoriesResult = await categoriesResponse.json();
        if (categoriesResult.data && Array.isArray(categoriesResult.data)) {
          // 按排序字段排序
          const sortedCategories = [...categoriesResult.data].sort((a, b) => a.sort_order - b.sort_order);
          setCategories(sortedCategories);
        } else {
          setCategories([]);
          console.error('Categories data is not an array:', categoriesResult);
        }
      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError('Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [restaurantId]);

  // 添加或更新菜品
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!dishName.trim()) {
      setError(t('dishNameRequired'));
      return;
    }

    if (!dishCategory) {
      setError(t('categoryRequired'));
      return;
    }

    const price = parseFloat(dishPriceStr);
    if (isNaN(price) || price <= 0) {
      setError(t('invalidPrice'));
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const endpoint = editingId
        ? `/api/kitchen/dishes/${editingId}`
        : '/api/kitchen/dishes';

      const method = editingId ? 'PUT' : 'POST';

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: dishName,
          description: dishDescription,
          price: parseFloat(dishPriceStr),
          categoryId: dishCategory,
          inputLanguage,
          sortOrder: editingId ? dishes.find(d => d.id === editingId)?.sort_order || 0 : dishes.length + 1,
          restaurantId,
          imageUrl: dishImage || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to save dish');
      }

      const result = await response.json();

      // 更新菜品列表
      if (editingId) {
        setDishes(dishes.map(dish =>
          dish.id === editingId ? result.data : dish
        ));
        setSuccess(t('dishUpdated'));
      } else {
        setDishes([...dishes, result.data]);
        setSuccess(t('dishAdded'));
      }

      // 重置表单并关闭模态框
      resetForm();
    } catch (error: any) {
      console.error('Error saving dish:', error);
      setError(error instanceof Error ? error.message : 'Failed to save dish');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 编辑菜品
  const handleEdit = (dish: Dish) => {
    setEditingId(dish.id);
    setDishName(inputLanguage === 'zh' ? dish.name_zh : dish.name_ja);
    setDishDescription(inputLanguage === 'zh' ? dish.description_zh : dish.description_ja);
    setDishPriceStr(dish.price.toString());
    setDishCategory(dish.category_id);
    setDishImage(dish.image_url || '');
    setError(null);
    setSuccess(null);
    setIsModalOpen(true); // 打开模态框
  };

  // 打开新增菜品模态框
  const handleAddNew = () => {
    resetForm();
    setIsModalOpen(true);
  };

  // 当输入语言变化时，更新表单内容
  useEffect(() => {
    if (editingId) {
      const currentDish = dishes.find(dish => dish.id === editingId);
      if (currentDish) {
        setDishName(inputLanguage === 'zh' ? currentDish.name_zh : currentDish.name_ja);
        setDishDescription(inputLanguage === 'zh' ? currentDish.description_zh : currentDish.description_ja);
      }
    }
  }, [inputLanguage, editingId, dishes]);

  // 删除菜品
  const handleDelete = async (id: string) => {
    if (!confirm(t('confirmDeleteDish'))) {
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/kitchen/dishes/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete dish');
      }

      // 从列表中移除
      setDishes(dishes.filter(dish => dish.id !== id));
      setSuccess(t('dishDeleted'));

      // 如果正在编辑被删除的菜品，重置表单
      if (editingId === id) {
        resetForm();
      }
    } catch (error: any) {
      console.error('Error deleting dish:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete dish');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setEditingId(null);
    setDishName('');
    setDishDescription('');
    setDishPriceStr('');
    setDishCategory('');
    setDishImage('');
    setError(null);
    setSuccess(null);
    setIsModalOpen(false); // 关闭模态框
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 找到拖拽的项目和目标位置
      const oldIndex = dishes.findIndex(dish => dish.id === active.id);
      const newIndex = dishes.findIndex(dish => dish.id === over.id);

      // 重新排序菜品列表
      const updatedDishes = arrayMove(dishes, oldIndex, newIndex);

      // 更新排序值
      const reorderedDishes = updatedDishes.map((dish, index) => ({
        ...dish,
        sort_order: index + 1 // 使用连续的数字作为排序值
      }));

      // 更新状态
      setDishes(reorderedDishes);

      // 发送请求到服务器更新排序
      const response = await fetch('/api/kitchen/dishes/reorder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dishes: reorderedDishes.map(dish => ({
            id: dish.id,
            sort_order: dish.sort_order
          })),
          restaurantId: restaurantId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update dish order');
      }

      setSuccess(t('dishOrderUpdated'));
    } catch (error: any) {
      console.error('Error updating dish order:', error);
      setError(error instanceof Error ? error.message : 'Failed to update dish order');

      // 如果出错，重新获取菜品列表
      if (restaurantId) {
        const fetchDishes = async () => {
          try {
            const response = await fetch(`/api/dishes?restaurantId=${restaurantId}`);
            if (!response.ok) {
              throw new Error('Failed to fetch dishes');
            }

            const result = await response.json();
            if (result.data && Array.isArray(result.data)) {
              const sortedDishes = [...result.data].sort((a, b) => a.sort_order - b.sort_order);
              setDishes(sortedDishes);
            }
          } catch (error: any) {
            console.error('Error fetching dishes:', error);
          }
        };

        fetchDishes();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果不是客户端渲染，显示加载中
  if (!isClient) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('dishManagement')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  // 如果正在加载，显示加载中
  if (isLoading) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('dishManagement')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  return (
    <KitchenLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">{t('dishManagement')}</h1>
          <button
            onClick={handleAddNew}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md font-medium flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {t('addDish')}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

        {/* 菜品列表 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          {dishes.length === 0 ? (
            <p className="text-gray-500 italic text-center py-8">{t('noDishes')}</p>
          ) : (
            <div className="overflow-x-auto">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('sortOrder')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('chinese')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('japanese')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('english')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('korean')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('category')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('price')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('actions')}
                      </th>
                    </tr>
                  </thead>
                  <SortableContext
                    items={dishes.map(dish => dish.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <tbody className="bg-white divide-y divide-gray-200">
                      {dishes.map((dish) => (
                        <SortableTableRow
                          key={dish.id}
                          dish={dish}
                          onEdit={handleEdit}
                          onDelete={handleDelete}
                          tCommon={tCommon}
                          categories={categories}
                          locale={locale}
                        />
                      ))}
                    </tbody>
                  </SortableContext>
                </table>
              </DndContext>
              <div className="mt-4 text-sm text-gray-500">
                <p>{t('dragToReorderHint')}</p>
              </div>
            </div>
          )}
        </div>

        {/* 模态框 */}
        <Modal
          isOpen={isModalOpen}
          onClose={resetForm}
          title={editingId ? t('editDish') : t('addDish')}
        >
          <form onSubmit={handleSubmit}>
            {/* 输入语言选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('inputLanguage')}
              </label>
              <p className="text-sm text-gray-500 mb-2">
                {t('inputLanguageHint')}
              </p>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="inputLanguage"
                    value="zh"
                    checked={inputLanguage === 'zh'}
                    onChange={() => setInputLanguage('zh')}
                  />
                  <span className="ml-2 text-sm text-gray-700">{tCommon('chinese')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="inputLanguage"
                    value="ja"
                    checked={inputLanguage === 'ja'}
                    onChange={() => setInputLanguage('ja')}
                  />
                  <span className="ml-2 text-sm text-gray-700">{tCommon('japanese')}</span>
                </label>
              </div>
            </div>

            {/* 菜品名称 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('dishName')}
              </label>
              <input
                type="text"
                value={dishName}
                onChange={(e) => setDishName(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder={inputLanguage === 'zh' ? '请输入中文名称' : '日本語名を入力してください'}
              />
            </div>

            {/* 菜品描述 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('dishDescription')}
              </label>
              <textarea
                value={dishDescription}
                onChange={(e) => setDishDescription(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                rows={3}
                placeholder={inputLanguage === 'zh' ? '请输入中文描述' : '日本語の説明を入力してください'}
              />
            </div>

            {/* 菜品价格 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('dishPrice')}
              </label>
              <div className="flex items-center">
                <span className="mr-2">¥</span>
                <input
                  type="text"
                  value={dishPriceStr}
                  onChange={(e) => {
                    // 只允许输入数字和小数点
                    const value = e.target.value;
                    if (value === '' || /^\d*\.?\d*$/.test(value)) {
                      setDishPriceStr(value);
                    }
                  }}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* 菜品分类 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('dishCategory')}
              </label>
              <select
                value={dishCategory}
                onChange={(e) => setDishCategory(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">{t('selectCategory')}</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {inputLanguage === 'zh' ? category.name_zh : category.name_ja}
                  </option>
                ))}
              </select>
            </div>

            {/* 菜品图片上传 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('dishImage')}
              </label>
              <ImageUploader
                onImageUploaded={(imageUrl) => setDishImage(imageUrl)}
                currentImageUrl={dishImage}
                onUploadStatusChange={setIsImageUploading}
                translations={{
                  upload: t('upload'),
                  dragDrop: t('dragDrop'),
                  or: t('or'),
                  browse: t('browse'),
                  uploading: t('uploading'),
                  maxSize: t('maxSize'),
                  invalidType: t('invalidType'),
                  uploadFailed: t('uploadFailed')
                }}
              />
            </div>

            {/* 按钮 */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <button
                type="button"
                onClick={resetForm}
                className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium"
              >
                {tCommon('cancel')}
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !dishName.trim() || !dishCategory || !dishPriceStr || parseFloat(dishPriceStr) <= 0 || isImageUploading}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium disabled:opacity-50"
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {editingId ? t('updating') : t('adding')}
                  </span>
                ) : (
                  editingId ? tCommon('save') : tCommon('add')
                )}
              </button>
            </div>
          </form>
        </Modal>
      </div>
    </KitchenLayout>
  );
}
