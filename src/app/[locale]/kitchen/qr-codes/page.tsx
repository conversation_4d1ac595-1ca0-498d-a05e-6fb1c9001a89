'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { QRCodeSVG } from 'qrcode.react';
import Image from 'next/image';
import { useReactToPrint } from 'react-to-print';
import KitchenLayout from '../../../../components/kitchen/KitchenLayout';
import { useRestaurantStore } from '@/store/restaurantStore';

type Table = {
  id: string;
  table_number: string;
  restaurant_id: string;
};

export default function KitchenQRCodesPage() {
  const t = useTranslations('kitchen');
  const commonT = useTranslations('common');
  const [tables, setTables] = useState<Table[]>([]);
  const [loading, setLoading] = useState(true);
  const [restaurantId, setRestaurantId] = useState<string>('');
  const [baseUrl, setBaseUrl] = useState<string>('');
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [logo, setLogo] = useState<string | null>(null);
  const [logoSize, setLogoSize] = useState<number>(50);
  const printRef = useRef<HTMLDivElement>(null);

  // 使用 Zustand store 获取餐厅 ID
  const { getRestaurantId } = useRestaurantStore();

  // 定义获取餐桌的函数
  const fetchTables = useCallback(async (restaurantId: string) => {
    setLoading(true);
    try {
      console.log('Fetching tables for restaurant ID:', restaurantId);

      // 检查 restaurantId 是否有效
      if (!restaurantId || restaurantId.trim() === '') {
        console.error('Invalid restaurant ID:', restaurantId);
        setTables([]);
        return;
      }

      // 先检查餐厅是否存在
      console.log('Verifying restaurant exists via API...');
      try {
        const restaurantResponse = await fetch(`/api/restaurants?id=${restaurantId}`);

        if (!restaurantResponse.ok) {
          const errorData = await restaurantResponse.json();
          console.error('Error verifying restaurant via API:', errorData.error);
        } else {
          const { data: restaurant } = await restaurantResponse.json();
          console.log('Restaurant found via API:', restaurant);
        }
      } catch (restaurantError) {
        console.error('Error checking restaurant via API:', restaurantError);
      }

      // 通过 API 获取餐桌数据
      console.log('Fetching tables via API...');
      const tablesResponse = await fetch(`/api/tables?restaurantId=${restaurantId}`);

      if (!tablesResponse.ok) {
        const errorData = await tablesResponse.json();
        throw new Error(errorData.error || 'Failed to fetch tables');
      }

      const { data } = await tablesResponse.json();

      console.log('Fetched tables via API:', data);

      if (!data || data.length === 0) {
        console.warn('No tables found for restaurant ID:', restaurantId);

        // 如果没有找到餐桌，尝试通过 API 创建一些示例餐桌
        console.log('Creating sample tables for restaurant via API...');

        // 准备餐桌数据
        const tableNumbers = [];
        for (let i = 1; i <= 5; i++) {
          tableNumbers.push(i.toString());
        }

        try {
          // 调用 API 创建餐桌
          const response = await fetch('/api/tables', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              restaurantId,
              tableNumbers,
            }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to create tables');
          }

          const { data: newTables } = await response.json();

          console.log('Created sample tables via API:', newTables);
          setTables(newTables);
          return;
        } catch (apiError) {
          console.error('Error creating sample tables via API:', apiError);
          setTables([]);
        }
      } else {
        setTables(data);
      }
    } catch (error: any) {
      console.error('Error fetching tables:', error);
      setTables([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化 - 设置基础 URL
  useEffect(() => {
    // 确保代码只在客户端执行
    if (typeof window !== 'undefined') {
      // 设置基础 URL
      setBaseUrl(window.location.origin);
      console.log('Base URL set to:', window.location.origin);
    }
  }, []);

  // 初始化 - 获取餐厅 ID
  useEffect(() => {
    // 从 localStorage 或 store 中获取餐厅 ID
    const id = getRestaurantId();
    if (id) {
      console.log('Using restaurant ID from store or localStorage:', id);
      setRestaurantId(id);
    } else {
      console.log('No restaurant ID found in store or localStorage');
      // 如果没有找到餐厅 ID，可以在这里添加默认处理逻辑
    }
  }, [getRestaurantId, setRestaurantId]);

  // 当餐厅 ID 变化时获取餐桌
  useEffect(() => {
    if (restaurantId) {
      console.log('Restaurant ID changed, fetching tables for:', restaurantId);
      fetchTables(restaurantId);
    } else {
      console.log('No restaurant ID available yet');
    }
  }, [restaurantId, fetchTables]);

  // 处理全选逻辑
  useEffect(() => {
    if (selectAll && tables.length > 0) {
      // 只有当 selectAll 为 true 且有餐桌时才设置所有餐桌为选中
      setSelectedTables(tables.map(table => table.id));
    }
  }, [selectAll, tables]);

  // 处理当所有餐桌都被选中时，自动设置 selectAll 为 true
  useEffect(() => {
    if (tables.length > 0 && selectedTables.length === tables.length && !selectAll) {
      setSelectAll(true);
    }
  }, [selectedTables, tables, selectAll]);

  const handleTableSelection = (tableId: string) => {
    setSelectedTables(prev => {
      if (prev.includes(tableId)) {
        const newSelection = prev.filter(id => id !== tableId);
        setSelectAll(newSelection.length === tables.length);
        return newSelection;
      } else {
        const newSelection = [...prev, tableId];
        setSelectAll(newSelection.length === tables.length);
        return newSelection;
      }
    });
  };

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    if (!selectAll) {
      setSelectedTables(tables.map(table => table.id));
    } else {
      setSelectedTables([]);
    }
  };

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setLogo(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: 'Restaurant QR Codes',
  });

  const downloadQRCode = (tableId: string, tableNumber: string) => {
    const canvas = document.getElementById(`qr-code-${tableId}`) as HTMLCanvasElement;
    if (canvas) {
      const pngUrl = canvas
        .toDataURL('image/png')
        .replace('image/png', 'image/octet-stream');

      const downloadLink = document.createElement('a');
      downloadLink.href = pngUrl;
      downloadLink.download = `qr-table-${tableNumber}.png`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  };

  const getQRCodeUrl = (tableId: string) => {
    // 获取当前语言
    const locale = typeof window !== 'undefined' ? window.location.pathname.split('/')[1] : 'zh';
    const url = `${baseUrl}/auth/${restaurantId}/${tableId}?source=scan`;
    console.log('Generated QR code URL:', url);
    return url;
  };

  return (
    <KitchenLayout>
      <div className="max-w-6xl mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">{t('qrCodes')}</h1>
          <div className="flex space-x-4">
            <button
              onClick={handlePrint}
              disabled={selectedTables.length === 0}
              className={`px-4 py-2 rounded-md ${
                selectedTables.length === 0
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {t('printSelected')} ({selectedTables.length})
            </button>
          </div>
        </div>

        {/* 调试信息 */}
        {/* <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-6">
          <p className="text-sm text-yellow-700">
            <strong>调试信息:</strong><br />
            餐厅 ID: {restaurantId}<br />
            餐桌数量: {tables.length}<br />
            加载状态: {loading ? '加载中' : '加载完成'}<br />
          </p>
        </div> */}

        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
          <p className="text-sm text-blue-700">
            <strong>{commonT('note')}:</strong> {t('qrCodeNote')}
          </p>
        </div>

        <div className="mb-6 p-4 border rounded-md bg-gray-50">
          <h2 className="text-lg font-semibold mb-4">{t('customizeQRCodes')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('uploadLogo')}
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-blue-50 file:text-blue-700
                  hover:file:bg-blue-100"
              />
              {logo && (
                <div className="mt-2">
                  <Image
                    src={logo}
                    alt="Logo Preview"
                    width={100}
                    height={100}
                    className="border rounded-md"
                  />
                </div>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('logoSize')}
              </label>
              <input
                type="range"
                min="20"
                max="80"
                value={logoSize}
                onChange={(e) => setLogoSize(Number(e.target.value))}
                className="w-full"
              />
              <div className="text-sm text-gray-500 mt-1">{logoSize}px</div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="select-all"
              checked={selectAll}
              onChange={handleSelectAll}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="select-all" className="ml-2 text-sm font-medium text-gray-700">
              {t('selectAll')}
            </label>
          </div>

          {loading ? (
            <div className="text-center py-10">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <p className="mt-2 text-gray-500">{commonT('loading')}</p>
            </div>
          ) : tables.length === 0 ? (
            <div className="text-center py-10 bg-gray-50 rounded-md">
              <p className="text-gray-500">{t('noTablesFound')}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {tables.map((table) => (
                <div
                  key={table.id}
                  className={`border rounded-md overflow-hidden ${
                    selectedTables.includes(table.id) ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="p-4">
                    <div className="flex justify-between items-center mb-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`table-${table.id}`}
                          checked={selectedTables.includes(table.id)}
                          onChange={() => handleTableSelection(table.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`table-${table.id}`} className="ml-2 font-medium">
                          {commonT('table')} {table.table_number}
                        </label>
                      </div>
                      {/* <button
                        onClick={() => downloadQRCode(table.id, table.table_number)}
                        className="text-blue-500 hover:text-blue-700"
                        title={t('downloadQRCode')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </button> */}
                    </div>
                    <div className="flex justify-center bg-white p-3 rounded-md">
                      <QRCodeSVG
                        id={`qr-code-${table.id}`}
                        value={getQRCodeUrl(table.id)}
                        size={150}
                        level="H"
                        marginSize={4}
                        imageSettings={logo ? {
                          src: logo,
                          excavate: true,
                          height: logoSize,
                          width: logoSize,
                        } : undefined}
                      />
                    </div>
                    <div className="mt-2 text-xs text-gray-500 text-center">
                      {commonT('table')} {table.table_number}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Hidden print area */}
        <div className="hidden">
          <div ref={printRef} className="p-5">
            <h1 className="text-xl font-bold mb-3 text-center">{t('restaurantQRCodes')}</h1>
            <div className="grid grid-cols-2 gap-8">
              {tables
                .filter(table => selectedTables.includes(table.id))
                .map((table) => (
                  <div key={table.id} className="flex flex-col items-center p-4 border rounded-md">
                    <h2 className="text-base font-bold mb-2">
                      {commonT('table')} {table.table_number}
                    </h2>
                    <QRCodeSVG
                      value={getQRCodeUrl(table.id)}
                      size={180}
                      level="H"
                      marginSize={4}
                      imageSettings={logo ? {
                        src: logo,
                        excavate: true,
                        height: logoSize,
                        width: logoSize,
                      } : undefined}
                    />
                    <p className="mt-1 text-sm text-center">
                      {/* {t('scanToOrder')} */}
                      スキャンして注文する
                    </p>
                    <p className="mt-0 text-xs text-center">
                      Scan to order
                    </p>
                    <p className="mt-0 text-xs text-center">
                      스캔하여 주문
                    </p>
                    <p className="mt-0 text-xs text-center">
                      扫码点单
                    </p>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
    </KitchenLayout>
  );
}
