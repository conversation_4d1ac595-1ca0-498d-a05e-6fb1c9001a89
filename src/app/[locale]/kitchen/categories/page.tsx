'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useRestaurantStore } from '@/store/restaurantStore';
import Loading from '@/components/common/Loading';
import KitchenLayout from '@/components/kitchen/KitchenLayout';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface Category {
  id: string;
  name_zh: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  restaurant_id: string;
  sort_order: number;
}

// 可排序的表格行组件
function SortableTableRow({
  category,
  onEdit,
  onDelete,
  tCommon
}: {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (id: string) => void;
  tCommon: any;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: category.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 0,
    position: 'relative' as const,
    cursor: 'grab'
  };

  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex items-center">
        <svg className="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
        </svg>
        {category.sort_order}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.name_zh}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.name_ja}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.name_en}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {category.name_ko}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <button
          onClick={() => onEdit(category)}
          className="text-indigo-600 hover:text-indigo-900 mr-3"
        >
          {tCommon('edit')}
        </button>
        <button
          onClick={() => onDelete(category.id)}
          className="text-red-600 hover:text-red-900"
        >
          {tCommon('delete')}
        </button>
      </td>
    </tr>
  );
}

export default function CategoriesPage() {
  const t = useTranslations('kitchen');
  const tCommon = useTranslations('common');
  const router = useRouter();
  const restaurantStore = useRestaurantStore();
  const restaurantId = restaurantStore.getRestaurantId();

  // 客户端渲染标志
  const [isClient, setIsClient] = useState(false);

  // 类别列表和表单状态
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 编辑状态
  const [editingId, setEditingId] = useState<string | null>(null);

  // 表单状态
  const [categoryName, setCategoryName] = useState('');
  const [inputLanguage, setInputLanguage] = useState<'zh' | 'ja'>('zh'); // 默认中文输入

  // 拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 在组件挂载后设置客户端渲染标志
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 获取类别列表
  useEffect(() => {
    if (!restaurantId) return;

    const fetchCategories = async () => {
      try {
        const response = await fetch(`/api/categories?restaurantId=${restaurantId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          // 按排序字段排序
          const sortedCategories = [...result.data].sort((a, b) => a.sort_order - b.sort_order);
          setCategories(sortedCategories);
        } else {
          setCategories([]);
          console.error('Categories data is not an array:', result);
        }
      } catch (error: any) {
        console.error('Error fetching categories:', error);
        setError('Failed to fetch categories');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, [restaurantId]);

  // 添加或更新类别
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryName.trim()) {
      setError(t('categoryNameRequired'));
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const endpoint = editingId
        ? `/api/kitchen/categories/${editingId}`
        : '/api/kitchen/categories';

      const method = editingId ? 'PUT' : 'POST';

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: categoryName,
          inputLanguage,
          sortOrder: editingId ? categories.find(c => c.id === editingId)?.sort_order || 0 : categories.length * 10,
          restaurantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to save category');
      }

      const result = await response.json();

      // 更新类别列表
      if (editingId) {
        setCategories(categories.map(cat =>
          cat.id === editingId ? result.data : cat
        ));
        setSuccess(t('categoryUpdated'));
      } else {
        setCategories([...categories, result.data]);
        setSuccess(t('categoryAdded'));
      }

      // 重置表单
      setCategoryName('');
      setEditingId(null);
    } catch (error: any) {
      console.error('Error saving category:', error);
      setError(error instanceof Error ? error.message : 'Failed to save category');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 编辑类别
  const handleEdit = (category: Category) => {
    setEditingId(category.id);
    setCategoryName(inputLanguage === 'zh' ? category.name_zh : category.name_ja);
    setError(null);
    setSuccess(null);
  };

  // 删除类别
  const handleDelete = async (id: string) => {
    if (!confirm(t('confirmDeleteCategory'))) {
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`/api/kitchen/categories/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete category');
      }

      // 从列表中移除
      setCategories(categories.filter(cat => cat.id !== id));
      setSuccess(t('categoryDeleted'));

      // 如果正在编辑被删除的类别，重置表单
      if (editingId === id) {
        setCategoryName('');
        setEditingId(null);
      }
    } catch (error: any) {
      console.error('Error deleting category:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete category');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setEditingId(null);
    setCategoryName('');
    setError(null);
    setSuccess(null);
  };

  // 处理拖拽结束
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    setIsSubmitting(true);

    try {
      // 找到拖拽的项目和目标位置
      const oldIndex = categories.findIndex(cat => cat.id === active.id);
      const newIndex = categories.findIndex(cat => cat.id === over.id);

      // 重新排序类别列表
      const updatedCategories = arrayMove(categories, oldIndex, newIndex);

      // 更新排序值
      const reorderedCategories = updatedCategories.map((cat, index) => ({
        ...cat,
        sort_order: index + 1 // 使用连续的数字作为排序值
      }));

      // 更新状态
      setCategories(reorderedCategories);

      // 发送请求到服务器更新排序
      const response = await fetch('/api/kitchen/categories/reorder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categories: reorderedCategories.map(cat => ({
            id: cat.id,
            sort_order: cat.sort_order
          })),
          restaurantId: restaurantId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update category order');
      }

      setSuccess(t('categoryOrderUpdated'));
    } catch (error: any) {
      console.error('Error updating category order:', error);
      setError(error instanceof Error ? error.message : 'Failed to update category order');

      // 如果出错，重新获取类别列表
      if (restaurantId) {
        const fetchCategories = async () => {
          try {
            const response = await fetch(`/api/categories?restaurantId=${restaurantId}`);
            if (!response.ok) {
              throw new Error('Failed to fetch categories');
            }

            const result = await response.json();
            if (result.data && Array.isArray(result.data)) {
              const sortedCategories = [...result.data].sort((a, b) => a.sort_order - b.sort_order);
              setCategories(sortedCategories);
            }
          } catch (error: any) {
            console.error('Error fetching categories:', error);
          }
        };

        fetchCategories();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 如果不是客户端渲染，显示加载中
  if (!isClient) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('categoryManagement')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  // 如果正在加载，显示加载中
  if (isLoading) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('categoryManagement')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  return (
    <KitchenLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">{t('categoryManagement')}</h1>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            {success}
          </div>
        )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 左侧：类别表单 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">
            {editingId ? t('editCategory') : t('addCategory')}
          </h2>

          <form onSubmit={handleSubmit}>
            {/* 输入语言选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('inputLanguage')}
              </label>
              <p className="text-sm text-gray-500 mb-2">
                {t('inputLanguageHint')}
              </p>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="inputLanguage"
                    value="zh"
                    checked={inputLanguage === 'zh'}
                    onChange={() => setInputLanguage('zh')}
                  />
                  <span className="ml-2 text-sm text-gray-700">{tCommon('chinese')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="inputLanguage"
                    value="ja"
                    checked={inputLanguage === 'ja'}
                    onChange={() => setInputLanguage('ja')}
                  />
                  <span className="ml-2 text-sm text-gray-700">{tCommon('japanese')}</span>
                </label>
              </div>
            </div>

            {/* 类别名称 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('categoryName')}
              </label>
              <input
                type="text"
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder={inputLanguage === 'zh' ? '请输入中文名称' : '日本語名を入力してください'}
              />
            </div>

            {/* 排序提示 */}
            <div className="mb-6">
              <p className="text-sm text-gray-500">
                {t('dragToReorder')}
              </p>
            </div>

            {/* 按钮 */}
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={isSubmitting || !categoryName.trim()}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium disabled:opacity-50 flex-1"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {editingId ? t('updating') : t('adding')}
                  </span>
                ) : (
                  editingId ? tCommon('save') : tCommon('add')
                )}
              </button>

              {editingId && (
                <button
                  type="button"
                  onClick={handleCancel}
                  className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md font-medium"
                >
                  {tCommon('cancel')}
                </button>
              )}
            </div>
          </form>
        </div>

        {/* 右侧：类别列表 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">{t('categories')}</h2>

          {categories.length === 0 ? (
            <p className="text-gray-500 italic text-center py-8">{t('noCategories')}</p>
          ) : (
            <div className="overflow-x-auto">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t('sortOrder')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('chinese')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('japanese')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('english')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('korean')}
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {tCommon('actions')}
                      </th>
                    </tr>
                  </thead>
                  <SortableContext
                    items={categories.map(cat => cat.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <tbody className="bg-white divide-y divide-gray-200">
                      {categories.map((category) => (
                        <SortableTableRow
                          key={category.id}
                          category={category}
                          onEdit={handleEdit}
                          onDelete={handleDelete}
                          tCommon={tCommon}
                        />
                      ))}
                    </tbody>
                  </SortableContext>
                </table>
              </DndContext>
              <div className="mt-4 text-sm text-gray-500">
                <p>{t('dragToReorderHint')}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    </KitchenLayout>
  );
}
