'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useRestaurantStore } from '@/store/restaurantStore';
import KitchenLayout from '../../../../components/kitchen/KitchenLayout';

// 定义表单输入类型
type FormInput = {
  tableNumber: string;
};

// 定义表格类型
type Table = {
  id: string;
  restaurant_id: string;
  table_number: string;
  created_at: string;
};

export default function TablesPage() {
  const t = useTranslations('kitchen');
  const tCommon = useTranslations('common');
  const [tables, setTables] = useState<Table[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingTable, setEditingTable] = useState<Table | null>(null);
  const [isAdding, setIsAdding] = useState(false);

  // 使用 Zustand store 获取餐厅 ID
  const { getRestaurantId } = useRestaurantStore();
  const restaurantId = getRestaurantId();

  // 使用 React Hook Form
  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors }
  } = useForm<FormInput>();

  // 获取餐桌数据
  const fetchTables = async () => {
    setLoading(true);
    try {
      if (!restaurantId) {
        throw new Error('Restaurant ID not found');
      }

      const response = await fetch(`/api/tables?restaurantId=${restaurantId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch tables');
      }

      const { data } = await response.json();
      setTables(data || []);
    } catch (error: any) {
      console.error('Error fetching tables:', error);
      setError(error.message || 'Failed to fetch tables');
    } finally {
      setLoading(false);
    }
  };

  // 添加餐桌
  const addTable: SubmitHandler<FormInput> = async (data) => {
    try {
      if (!restaurantId) {
        throw new Error('Restaurant ID not found');
      }

      const response = await fetch('/api/tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restaurantId,
          tableNumbers: [data.tableNumber],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add table');
      }

      // 重新获取餐桌数据
      fetchTables();

      // 重置表单
      reset();
      setIsAdding(false);
    } catch (error: any) {
      console.error('Error adding table:', error);
      setError(error.message || 'Failed to add table');
    }
  };

  // 更新餐桌
  const updateTable: SubmitHandler<FormInput> = async (data) => {
    try {
      if (!editingTable) {
        throw new Error('No table selected for editing');
      }

      const response = await fetch('/api/tables', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tableId: editingTable.id,
          tableNumber: data.tableNumber,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update table');
      }

      // 重新获取餐桌数据
      fetchTables();

      // 重置表单
      reset();
      setEditingTable(null);
    } catch (error: any) {
      console.error('Error updating table:', error);
      setError(error.message || 'Failed to update table');
    }
  };

  // 删除餐桌
  const deleteTable = async (tableId: string) => {
    if (!confirm(t('confirmDeleteTable'))) {
      return;
    }

    try {
      const response = await fetch(`/api/tables?id=${tableId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete table');
      }

      // 重新获取餐桌数据
      fetchTables();
    } catch (error: any) {
      console.error('Error deleting table:', error);
      setError(error.message || 'Failed to delete table');
    }
  };

  // 开始编辑餐桌
  const startEditing = (table: Table) => {
    setEditingTable(table);
    setValue('tableNumber', table.table_number);
  };

  // 取消编辑
  const cancelEditing = () => {
    setEditingTable(null);
    reset();
  };

  // 初始化 - 获取餐桌数据
  useEffect(() => {
    if (restaurantId) {
      fetchTables();
    }
  }, [restaurantId]);

  return (
    <KitchenLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">{t('tableManagement')}</h1>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* 添加/编辑表单 */}
        <div className="bg-white shadow-md rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">
            {editingTable ? t('editTable') : t('addTable')}
          </h2>

          <form onSubmit={handleSubmit(editingTable ? updateTable : addTable)} className="space-y-4">
            <div>
              <label htmlFor="tableNumber" className="block text-sm font-medium text-gray-700 mb-1">
                {t('tableNumber')}
              </label>
              <input
                id="tableNumber"
                type="text"
                {...register('tableNumber', {
                  required: t('tableNumberRequired'),
                  pattern: {
                    value: /^[A-Za-z0-9-_]+$/,
                    message: t('tableNumberInvalid')
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder={t('tableNumberPlaceholder')}
              />
              {errors.tableNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.tableNumber.message}</p>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                type="submit"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {editingTable ? tCommon('save') : t('addTable')}
              </button>

              {editingTable && (
                <button
                  type="button"
                  onClick={cancelEditing}
                  className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  {tCommon('cancel')}
                </button>
              )}
            </div>
          </form>
        </div>

        {/* 餐桌列表 */}
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 className="text-lg font-medium leading-6 text-gray-900">{t('tables')}</h3>
          </div>

          {loading ? (
            <div className="p-6 text-center text-gray-500">{tCommon('loading')}</div>
          ) : tables.length === 0 ? (
            <div className="p-6 text-center text-gray-500">{t('noTables')}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('tableNumber')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('createdAt')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {tCommon('actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tables.map((table) => (
                    <tr key={table.id} className={editingTable?.id === table.id ? 'bg-blue-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {table.table_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(table.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => startEditing(table)}
                          className="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          {tCommon('edit')}
                        </button>
                        <button
                          onClick={() => deleteTable(table.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          {tCommon('delete')}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </KitchenLayout>
  );
}
