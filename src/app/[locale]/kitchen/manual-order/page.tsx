'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useRestaurantStore } from '@/store/restaurantStore';
import Loading from '@/components/common/Loading';
import KitchenLayout from '@/components/kitchen/KitchenLayout';
import { getDishName, getCategoryName } from '@/lib/i18n-utils';

interface Table {
  id: string;
  table_number: string;
}

interface Dish {
  id: string;
  name_zh: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
  price: number;
  category_id: string;
}

interface Category {
  id: string;
  name_zh: string;
  name_en: string;
  name_ja: string;
  name_ko: string;
}

interface OrderItem {
  dish: Dish;
  quantity: number;
}

export default function ManualOrderPage() {
  const t = useTranslations('kitchen');
  const tCommon = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const restaurantStore = useRestaurantStore();
  // 使用 getRestaurantId 方法获取餐厅 ID
  const restaurantId = restaurantStore.getRestaurantId();

  // 添加客户端渲染标志，避免 hydration 错误
  const [isClient, setIsClient] = useState(false);

  // 在组件挂载后设置客户端渲染标志
  useEffect(() => {
    setIsClient(true);

    // 从本地存储加载临时订单项目
    if (typeof window !== 'undefined') {
      const savedItems = localStorage.getItem('manual_order_items');
      if (savedItems) {
        try {
          const parsedItems = JSON.parse(savedItems);
          if (Array.isArray(parsedItems) && parsedItems.length > 0) {
            setOrderItems(parsedItems);
            // 如果有保存的订单项目，自动进入步骤 2
            setStep(2);
          }
        } catch (e) {
          console.error('Error parsing saved order items:', e);
          localStorage.removeItem('manual_order_items');
        }
      }
    }
  }, []);

  const [tables, setTables] = useState<Table[]>([]);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [guestCount, setGuestCount] = useState<number>(1);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [selectedDish, setSelectedDish] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [hasActiveOrder, setHasActiveOrder] = useState<boolean>(false);

  // 添加步骤状态
  const [step, setStep] = useState<number>(1); // 1: 选择餐桌和人数, 2: 添加菜品, 3: 提交订单
  const [orderType, setOrderType] = useState<'new' | 'append'>('new'); // 新单或追加

  // 获取餐桌列表
  useEffect(() => {
    const fetchTables = async () => {
      try {
        const response = await fetch(`/api/tables?restaurantId=${restaurantId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch tables');
        }
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          setTables(result.data);
        } else {
          // 如果返回的不是数组，则设置为空数组
          setTables([]);
          console.error('Tables data is not an array:', result);
        }
      } catch (error: any) {
        console.error('Error fetching tables:', error);
        setError('Failed to fetch tables');
      }
    };

    // 获取菜品列表
    const fetchDishes = async () => {
      try {
        const response = await fetch(`/api/dishes?restaurantId=${restaurantId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch dishes');
        }
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          setDishes(result.data);
        } else {
          // 如果返回的不是数组，则设置为空数组
          setDishes([]);
          console.error('Dishes data is not an array:', result);
        }
      } catch (error: any) {
        console.error('Error fetching dishes:', error);
        setError('Failed to fetch dishes');
      }
    };

    // 获取分类列表
    const fetchCategories = async () => {
      try {
        const response = await fetch(`/api/categories?restaurantId=${restaurantId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          setCategories(result.data);
        } else {
          // 如果返回的不是数组，则设置为空数组
          setCategories([]);
          console.error('Categories data is not an array:', result);
        }
      } catch (error: any) {
        console.error('Error fetching categories:', error);
        setError('Failed to fetch categories');
      } finally {
        setIsLoading(false);
      }
    };

    if (restaurantId) {
      fetchTables();
      fetchDishes();
      fetchCategories();
    } else {
      setIsLoading(false);
      setError('Restaurant ID not found');
    }
  }, [restaurantId]);

  // 添加菜品到订单
  const addDishToOrder = () => {
    if (!selectedDish || quantity <= 0) return;

    const dish = dishes.find(d => d.id === selectedDish);
    if (!dish) return;

    // 检查是否已经添加过该菜品
    const existingItemIndex = orderItems.findIndex(item => item.dish.id === selectedDish);

    let updatedItems: OrderItem[] = [];

    if (existingItemIndex >= 0) {
      // 更新已有菜品的数量
      updatedItems = [...orderItems];
      updatedItems[existingItemIndex].quantity += quantity;
    } else {
      // 添加新菜品
      updatedItems = [...orderItems, { dish, quantity }];
    }

    // 更新状态
    setOrderItems(updatedItems);

    // 保存到本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem('manual_order_items', JSON.stringify(updatedItems));
    }

    // 重置选择
    setSelectedDish('');
    setQuantity(1);
  };

  // 移除菜品
  const removeDish = (index: number) => {
    const updatedItems = [...orderItems];
    updatedItems.splice(index, 1);
    setOrderItems(updatedItems);

    // 保存到本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem('manual_order_items', JSON.stringify(updatedItems));
    }
  };

  // 更新菜品数量
  const updateQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeDish(index);
      return;
    }

    const updatedItems = [...orderItems];
    updatedItems[index].quantity = newQuantity;
    setOrderItems(updatedItems);

    // 保存到本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem('manual_order_items', JSON.stringify(updatedItems));
    }
  };

  // 计算总价
  const calculateTotal = () => {
    return orderItems.reduce((total, item) => total + (item.dish.price * item.quantity), 0);
  };

  // 检查餐桌是否有进行中的订单
  const checkActiveOrder = async (tableId: string) => {
    try {
      const response = await fetch(`/api/kitchen/active-orders?tableId=${tableId}&restaurantId=${restaurantId}`);
      if (!response.ok) {
        throw new Error('Failed to check active orders');
      }

      const data = await response.json();
      setHasActiveOrder(data.hasActiveOrder);

      // 如果有进行中的订单，默认设置为追加模式
      if (data.hasActiveOrder) {
        setOrderType('append');
      } else {
        setOrderType('new');
      }

      return data.hasActiveOrder;
    } catch (error: any) {
      console.error('Error checking active orders:', error);
      return false;
    }
  };

  // 当选择餐桌时检查是否有进行中的订单
  useEffect(() => {
    if (selectedTable) {
      checkActiveOrder(selectedTable);
    }
  }, [selectedTable]);

  // 创建初始订单（步骤 1）
  const createInitialOrder = () => {
    if (!selectedTable) {
      setError(t('selectTable'));
      return;
    }

    if (guestCount < 1) {
      setError(t('guestCount'));
      return;
    }

    // 保存餐桌和人数信息到本地存储
    if (typeof window !== 'undefined') {
      localStorage.setItem('manual_order_table_id', selectedTable);
      localStorage.setItem('manual_order_guest_count', guestCount.toString());
    }

    // 进入步骤 2
    setStep(2);
    setError(null);
  };

  // 作废订单
  const cancelOrder = () => {
    // 清空本地存储
    if (typeof window !== 'undefined') {
      localStorage.removeItem('manual_order_items');
      localStorage.removeItem('manual_order_table_id');
      localStorage.removeItem('manual_order_guest_count');
    }

    // 重置状态
    setOrderItems([]);
    setSelectedTable('');
    setGuestCount(1);
    setStep(1);
    setError(null);
  };

  // 提交订单
  const submitOrder = async () => {
    if (orderItems.length === 0) {
      setError(t('selectDishes'));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // 获取本地存储的餐桌和人数信息
      const tableId = selectedTable || localStorage.getItem('manual_order_table_id') || '';
      const guests = guestCount || parseInt(localStorage.getItem('manual_order_guest_count') || '1', 10);

      if (!tableId) {
        throw new Error('Table ID not found');
      }

      // 创建订单
      const response = await fetch('/api/kitchen/manual-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restaurantId,
          tableId,
          guestCount: guests,
          orderType, // 添加订单类型
          items: orderItems.map(item => ({
            dishId: item.dish.id,
            quantity: item.quantity,
            price: item.dish.price
          }))
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Order creation failed:', { status: response.status, errorData });
        throw new Error(`Failed to create order: ${errorData.error || response.statusText}`);
      }

      const data = await response.json();
      setSuccess(t('orderCreated'));
      setOrderId(data.orderId);

      // 清空本地存储
      if (typeof window !== 'undefined') {
        localStorage.removeItem('manual_order_items');
        localStorage.removeItem('manual_order_table_id');
        localStorage.removeItem('manual_order_guest_count');
      }

      // 重置表单
      setSelectedTable('');
      setGuestCount(1);
      setOrderItems([]);
      setStep(1);
    } catch (error: any) {
      console.error('Error creating order:', error);
      setError('Failed to create order');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 使用工具函数获取本地化名称
  const getLocalizedDishName = (dish: Dish) => getDishName(dish, locale);
  const getLocalizedCategoryName = (category: Category) => getCategoryName(category, locale);

  // 按分类筛选菜品
  const filteredDishes = selectedCategory
    ? dishes.filter(dish => dish.category_id === selectedCategory)
    : dishes;

  // 如果不是客户端渲染，显示加载中
  if (!isClient) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('createManualOrder')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  // 客户端渲染后，根据状态显示不同内容
  if (isLoading) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold mb-6">{t('createManualOrder')}</h1>
          <Loading />
        </div>
      </KitchenLayout>
    );
  }

  if (success && orderId) {
    return (
      <KitchenLayout>
        <div className="p-6">
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            <h2 className="font-bold">{success}</h2>
            <p>{t('orderCreatedMessage')} {orderId}</p>
          </div>
          <div className="flex gap-4 mt-4">
            <button
              onClick={() => router.push('/kitchen')}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              {t('backToKitchen')}
            </button>
            <button
              onClick={() => {
                setSuccess(null);
                setOrderId(null);
                setSelectedTable('');
                setGuestCount(1);
                setOrderItems([]);
              }}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
            >
              {t('createManualOrder')}
            </button>
          </div>
        </div>
      </KitchenLayout>
    );
  }

  // 渲染步骤 1：选择餐桌和人数
  const renderStep1 = () => {
    return (
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">{t('createManualOrder')}</h2>

        {/* 选择餐桌 */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('selectTable')}
          </label>
          <select
            value={selectedTable}
            onChange={(e) => setSelectedTable(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="">{t('selectTable')}</option>
            {tables.map((table) => (
              <option key={table.id} value={table.id}>
                {table.table_number}
              </option>
            ))}
          </select>
        </div>

        {/* 用餐人数 */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('guestCount')}
          </label>
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => setGuestCount(Math.max(1, guestCount - 1))}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l"
            >
              -
            </button>
            <div className="bg-gray-100 py-2 px-6 text-center min-w-[60px]">
              {guestCount}
            </div>
            <button
              type="button"
              onClick={() => setGuestCount(guestCount + 1)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r"
            >
              +
            </button>
          </div>
        </div>

        {/* 创建订单按钮 */}
        <button
          onClick={createInitialOrder}
          disabled={!selectedTable || guestCount < 1}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-md font-medium disabled:opacity-50"
        >
          {t('createOrder')}
        </button>
      </div>
    );
  };

  // 渲染步骤 2：添加菜品
  const renderStep2 = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 左侧：已添加菜品列表 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{t('items')}</h2>

            {/* 订单类型选择 */}
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="orderType"
                    value="new"
                    checked={orderType === 'new'}
                    onChange={() => setOrderType('new')}
                  />
                  <span className="ml-2 text-sm text-gray-700">{t('newOrder')}</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio text-blue-600"
                    name="orderType"
                    value="append"
                    checked={orderType === 'append'}
                    onChange={() => setOrderType('append')}
                    disabled={!hasActiveOrder}
                  />
                  <span className={`ml-2 text-sm ${hasActiveOrder ? 'text-gray-700' : 'text-gray-400'}`}>
                    {t('appendOrder')}
                  </span>
                </label>
              </div>

              {/* 显示是否有进行中的订单 */}
              {hasActiveOrder ? (
                <div className="text-xs text-green-600 font-medium">
                  {t('hasActiveOrder')}
                </div>
              ) : (
                <div className="text-xs text-gray-500">
                  {t('noActiveOrder')}
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          {orderItems.length > 0 && (
            <div className="flex space-x-2 mb-4">
              <button
                onClick={cancelOrder}
                disabled={isSubmitting}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md font-medium disabled:opacity-50"
              >
                {t('cancelOrder')}
              </button>
              <button
                onClick={submitOrder}
                disabled={isSubmitting}
                className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md font-medium disabled:opacity-50"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {t('submitOrder')}
                  </span>
                ) : (
                  t('submitOrder')
                )}
              </button>
            </div>
          )}

          {/* 已添加菜品列表 */}
          <div className="max-h-[calc(100vh-300px)] overflow-y-auto">
            {orderItems.length === 0 ? (
              <p className="text-gray-500 italic text-center py-8">{t('noOrders')}</p>
            ) : (
              <div className="divide-y">
                {orderItems.map((item, index) => (
                  <div key={index} className="py-3 flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium text-gray-700">{getLocalizedDishName(item.dish)}</p>
                      <div className="flex items-center mt-1">
                        <button
                          onClick={() => updateQuantity(index, item.quantity - 1)}
                          className="text-gray-600 hover:text-gray-800"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                          </svg>
                        </button>
                        <span className="mx-2 text-gray-600">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(index, item.quantity + 1)}
                          className="text-gray-600 hover:text-gray-800"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-600 mr-3">${(item.dish.price * item.quantity).toFixed(2)}</span>
                      <button
                        onClick={() => removeDish(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}

                <div className="pt-4 mt-4">
                  <div className="flex justify-between font-semibold text-gray-700">
                    <span>{tCommon('total')}</span>
                    <span>¥{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧：菜品选择 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold mb-4">{t('selectDishes')}</h2>

          {/* 分类筛选 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {tCommon('category')}
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
            >
              <option value="">{tCommon('all')}</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {getLocalizedCategoryName(category)}
                </option>
              ))}
            </select>
          </div>

          {/* 菜品选择和数量 */}
          <div className="mb-4">
            <div className="grid grid-cols-1 gap-4">
              {/* 选择的菜品 */}
              {selectedDish && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium">
                      {getLocalizedDishName(dishes.find(d => d.id === selectedDish) || { id: '', name_zh: '', name_en: '', name_ja: '', name_ko: '', price: 0, category_id: '' })}
                    </span>
                    <span className="text-gray-600">
                      ¥{(dishes.find(d => d.id === selectedDish)?.price || 0).toFixed(2)}
                    </span>
                  </div>

                  {/* 数量选择 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button
                        type="button"
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-3 rounded-l"
                      >
                        -
                      </button>
                      <div className="bg-white border-t border-b border-gray-300 py-1 px-4 text-center min-w-[40px]">
                        {quantity}
                      </div>
                      <button
                        type="button"
                        onClick={() => setQuantity(quantity + 1)}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-1 px-3 rounded-r"
                      >
                        +
                      </button>
                    </div>

                    <button
                      onClick={addDishToOrder}
                      className="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded-md text-sm font-medium"
                    >
                      {tCommon('add')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 菜品列表 */}
          <div className="mt-4">
            <div className="max-h-[calc(100vh-350px)] overflow-y-auto mt-2">
              <div className="grid grid-cols-1 gap-2">
                {filteredDishes.map((dish) => (
                  <div
                    key={dish.id}
                    onClick={() => setSelectedDish(dish.id)}
                    className={`p-3 border rounded-md cursor-pointer ${
                      selectedDish === dish.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex justify-between">
                      <span className="font-medium">{getLocalizedDishName(dish)}</span>
                      <span className="text-gray-600">¥{dish.price.toFixed(2)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <KitchenLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">{t('createManualOrder')}</h1>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* 根据步骤渲染不同内容 */}
        {step === 1 ? renderStep1() : renderStep2()}
      </div>
    </KitchenLayout>
  );
}
