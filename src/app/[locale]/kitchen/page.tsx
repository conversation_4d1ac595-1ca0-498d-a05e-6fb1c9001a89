'use client';

import { Suspense, useState } from 'react';
import { useTranslations } from 'next-intl';
import KitchenOrderList from '@/components/kitchen/KitchenOrderList';
import KitchenOrderHistory from '@/components/kitchen/KitchenOrderHistory';
import KitchenStatsFooter from '@/components/kitchen/KitchenStatsFooter';
import Loading from '@/components/common/Loading';
import KitchenLayout from '@/components/kitchen/KitchenLayout';

export default function KitchenPage() {
  const t = useTranslations('kitchen');
  const [refreshKey, setRefreshKey] = useState(0);
  const [showHistory, setShowHistory] = useState(false);

  // 手动刷新数据
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  // 显示历史订单
  const handleShowHistory = () => {
    setShowHistory(true);
  };

  // 关闭历史订单
  const handleCloseHistory = () => {
    setShowHistory(false);
  };

  return (
    <KitchenLayout>
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="mb-4 flex items-center">
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            title={t('refresh')}
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
          <button
            onClick={handleShowHistory}
            className="ml-2 px-3 py-1 bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {t('orderHistory')}
          </button>
        </div>

        <Suspense fallback={<Loading />}>
          <KitchenOrderList key={refreshKey} />
        </Suspense>

        {/* 历史订单弹窗 */}
        {showHistory && <KitchenOrderHistory onClose={handleCloseHistory} />}

        {/* 底部统计信息栏 */}
        <div className="mt-auto">
          <KitchenStatsFooter />
        </div>
      </div>
    </KitchenLayout>
  );
}
