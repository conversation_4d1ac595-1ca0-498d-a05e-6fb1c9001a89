'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLocale } from 'next-intl';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Head from 'next/head';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function PricingPage() {
  const t = useTranslations('pricing');
  const locale = useLocale();
  const [isYearly, setIsYearly] = useState(false);
  const [isFreeEligible, setIsFreeEligible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check if we're still eligible for the first 10 free restaurants
  useEffect(() => {
    async function checkFreeEligibility() {
      try {
        const response = await fetch('/api/restaurants/count');
        if (response.ok) {
          const data = await response.json();
          setIsFreeEligible(data.count < 10);
        }
      } catch (error) {
        console.error('Error checking free eligibility:', error);
      } finally {
        setIsLoading(false);
      }
    }

    checkFreeEligibility();
  }, []);

  // Features for each plan
  const basicFeatures = [
    t('featureQROrdering'),
    t('featureMultilingual'),
    t('featureKitchenManagement'),
    t('featureBasicAnalytics'),
    t('featureCustomerSupport'),
  ];

  const plusFeatures = [
    ...basicFeatures,
    t('featureElectronicPayment'),
    t('featureAdvancedAnalytics'),
    t('featurePrioritySupport'),
  ];

  const premiumFeatures = [
    ...plusFeatures,
    t('featureLoyaltyProgram'),
    t('featureCustomization'),
    t('feature24Support'),
    t('featureAPIAccess'),
  ];

  // FAQ items
  const faqItems = [
    {
      question: t('faqQuestion1'),
      answer: t('faqAnswer1'),
    },
    {
      question: t('faqQuestion2'),
      answer: t('faqAnswer2'),
    },
    {
      question: t('faqQuestion3'),
      answer: t('faqAnswer3'),
    },
    {
      question: t('faqQuestion4'),
      answer: t('faqAnswer4'),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <Head>
        <title>Pricing Plans - Kyoto Kitchen QR</title>
        <meta name="description" content="Choose the plan that fits your restaurant's needs. Basic, Plus, and Premium plans available with different features and pricing options." />
        <meta name="keywords" content="QR code ordering pricing, restaurant ordering system plans, digital menu pricing, contactless ordering subscription" />
      </Head>
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <Link href={`/${locale}/landing`} className="text-2xl font-bold text-blue-600">
              Kyoto Kitchen QR
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/landing`} className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link href={`/${locale}/register`} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
              {t('getStarted')}
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Pricing Header */}
      <motion.section
        className="py-16 bg-white"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-gray-600 mb-8">{t('subtitle')}</p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-12">
            <span className={`mr-4 ${!isYearly ? 'font-semibold text-blue-600' : 'text-gray-500'}`}>
              {t('monthly')}
            </span>
            <button
              onClick={() => setIsYearly(!isYearly)}
              className="relative inline-flex h-6 w-12 items-center rounded-full bg-gray-300"
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${isYearly ? 'translate-x-7' : 'translate-x-1'}`} />
            </button>
            <span className={`ml-4 ${isYearly ? 'font-semibold text-blue-600' : 'text-gray-500'}`}>
              {t('yearly')}
            </span>
          </div>
        </div>
      </motion.section>

      {/* Pricing Plans */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            {/* Basic Plan */}
            <motion.div
              className="bg-white rounded-lg shadow-lg overflow-hidden"
              variants={fadeIn}
            >
              <div className="p-6 border-b">
                <h3 className="text-2xl font-bold text-center">{t('basic')}</h3>
                <div className="mt-4 text-center">
                  <span className="text-4xl font-bold">
                    {isYearly ? t('basicYearlyPrice') : t('basicPrice')}
                  </span>
                  <span className="text-gray-500 ml-1">
                    {isYearly ? t('perYear') : t('perMonth')}
                  </span>
                </div>
                <p className="text-center text-gray-600 mt-2">{t('basicDesc')}</p>
              </div>
              <div className="p-6">
                <ul className="space-y-3">
                  {basicFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="mt-6 text-center">
                  <div className="space-y-2 mb-4">
                    <p className="text-yellow-500 font-medium">{t('freeTrial')}</p>
                    <p className="text-sm text-gray-600">
                      {isFreeEligible ? t('firstTenFree') : t('oneMonthTrial')}
                    </p>
                  </div>
                  <Link href={`/${locale}/register`} className="block w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md">
                    {t('getStarted')}
                  </Link>
                </div>
              </div>
            </motion.div>

            {/* Plus Plan */}
            <motion.div
              className="bg-white rounded-lg shadow-lg overflow-hidden border-2 border-blue-500 transform scale-105"
              variants={fadeIn}
            >
              <div className="p-6 border-b bg-blue-50">
                <h3 className="text-2xl font-bold text-center">{t('plus')}</h3>
                <div className="mt-4 text-center">
                  <span className="text-4xl font-bold">
                    {isYearly ? t('plusYearlyPrice') : t('plusPrice')}
                  </span>
                  <span className="text-gray-500 ml-1">
                    {isYearly ? t('perYear') : t('perMonth')}
                  </span>
                </div>
                <p className="text-center text-gray-600 mt-2">{t('plusDesc')}</p>
              </div>
              <div className="p-6">
                <ul className="space-y-3">
                  {plusFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="mt-6 text-center">
                  <p className="text-blue-500 font-medium mb-4">{t('comingSoon')}</p>
                  <button className="block w-full bg-gray-400 text-white py-2 px-4 rounded-md cursor-not-allowed">
                    {t('contactUs')}
                  </button>
                </div>
              </div>
            </motion.div>

            {/* Premium Plan */}
            <motion.div
              className="bg-white rounded-lg shadow-lg overflow-hidden"
              variants={fadeIn}
            >
              <div className="p-6 border-b">
                <h3 className="text-2xl font-bold text-center">{t('premium')}</h3>
                <div className="mt-4 text-center">
                  <span className="text-4xl font-bold">
                    {isYearly ? t('premiumYearlyPrice') : t('premiumPrice')}
                  </span>
                  <span className="text-gray-500 ml-1">
                    {isYearly ? t('perYear') : t('perMonth')}
                  </span>
                </div>
                <p className="text-center text-gray-600 mt-2">{t('premiumDesc')}</p>
              </div>
              <div className="p-6">
                <ul className="space-y-3">
                  {premiumFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="mt-6 text-center">
                  <p className="text-blue-500 font-medium mb-4">{t('comingSoon')}</p>
                  <button className="block w-full bg-gray-400 text-white py-2 px-4 rounded-md cursor-not-allowed">
                    {t('contactUs')}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <h2 className="text-3xl font-bold mb-4">{t('faq')}</h2>
            <div className="w-20 h-1 bg-blue-600 mx-auto"></div>
          </motion.div>

          <motion.div
            className="max-w-3xl mx-auto"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {faqItems.map((item, index) => (
              <motion.div
                key={index}
                className="mb-6 bg-gray-50 rounded-lg p-6"
                variants={fadeIn}
              >
                <h3 className="text-xl font-semibold mb-2">{item.question}</h3>
                <p className="text-gray-600">{item.answer}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <h2 className="text-3xl font-bold mb-6">{t('getStarted')}</h2>
            <p className="text-xl mb-8">{t('freeTrial')}</p>
            <Link href={`/${locale}/register`} className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-md font-medium text-lg inline-block">
              {t('registerNow')}
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p>&copy; 2024 Kyoto Kitchen QR. All rights reserved.</p>
          <p className="mt-2">{t('contactEmail')}</p>
        </div>
      </footer>
    </div>
  );
}
