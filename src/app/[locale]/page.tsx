'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import Loading from '@/components/common/Loading';

export default function HomePage() {
  const router = useRouter();
  const locale = useLocale();

  useEffect(() => {
    // Redirect to the landing page
    router.replace(`/${locale}/landing`);
  }, [router, locale]);

  return <Loading />;
}
