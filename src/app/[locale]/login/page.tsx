'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
// import Link from 'next/link'; // 未使用，已注释
import { useRestaurantStore } from '@/store/restaurantStore';

export default function LoginPage() {
  const t = useTranslations('auth');
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // 使用 Zustand store 设置餐厅 ID
  const { setRestaurantId, clearRestaurantId } = useRestaurantStore();

  // 添加调试信息
  console.log('登录页 - API URL:', '/api/auth');

  const handleLogin = async (e: React.FormEvent) => {
    // 阻止默认表单提交行为，防止表单数据出现在URL中
    e.preventDefault();

    // 确保表单不会通过GET方法提交
    if (e.target instanceof HTMLFormElement) {
      e.target.setAttribute('method', 'post');
    }

    if (!email || !password) {
      setError(t('emailPasswordRequired'));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // 清理之前的登录状态
      console.log('清理之前的登录状态...');
      localStorage.removeItem('restaurant_id');
      localStorage.removeItem('auth_token');
      localStorage.removeItem('table_id');
      localStorage.removeItem('token_expiry');
      localStorage.removeItem('order_id');
      localStorage.removeItem('guest_count');

      // 清理 Zustand store
      clearRestaurantId();

      // 通过 API 登录
      console.log('尝试通过 API 登录...');
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();

      // 打印登录结果
      console.log('登录结果: 成功');

      if (data?.user) {
        // 获取用户角色
        // @ts-ignore - 类型定义问题
        const roleName = data.userData?.roles?.name;

        // 保存餐厅 ID 到 localStorage 和 Zustand store
        if (data.userData?.restaurant_id) {
          console.log('保存餐厅 ID 到 localStorage 和 Zustand store:', data.userData.restaurant_id);
          localStorage.setItem('restaurant_id', data.userData.restaurant_id);
          setRestaurantId(data.userData.restaurant_id);
        } else {
          console.log('用户没有关联的餐厅');
        }

        // 获取当前语言
        const locale = window.location.pathname.split('/')[1];

        // 添加调试信息
        console.log('登录成功，用户角色:', roleName);
        console.log('会话信息:', data.session);

        // 确保会话已经被保存
        if (data.session) {
          // 打印会话令牌
          console.log('会话令牌:', data.session.access_token.substring(0, 10) + '...');

          // 添加延迟，确保会话已经被保存并且可以在其他组件中访问
          setTimeout(() => {
            try {
              console.log('准备跳转，用户角色:', roleName);

              // 根据角色重定向到不同页面
              if (roleName === 'super_admin' || roleName === 'admin') {
                console.log('跳转到管理员页面');
                router.push(`/${locale}/admin`);
              } else if (roleName === 'kitchen') {
                console.log('跳转到厨房页面');
                router.push(`/${locale}/kitchen`);
              } else {
                // 默认重定向
                console.log('跳转到首页');
                router.push(`/${locale}`);
              }
            } catch (e) {
              console.error('跳转错误:', e);
            }
          }, 2000); // 增加到2000毫秒延迟
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || t('loginFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {t('signIn')}
          </h2>
        </div>

        <form className="mt-8 space-y-6" method="post" action="javascript:void(0);" onSubmit={handleLogin}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                {t('email')}
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder={t('email')}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                {t('password')}
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                autoCapitalize="none"
                autoCorrect="off"
                spellCheck="false"
                autoSave="off"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder={t('password')}
              />
            </div>
          </div>

          {error && (
            <div className="text-red-500 text-sm">{error}</div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                loading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {loading ? t('signingIn') : t('signIn')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
