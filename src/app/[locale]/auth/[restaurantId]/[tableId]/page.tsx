import AuthPageClient from './client-page';

// 服务器组件
export default async function AuthPage({
  params
}: {
  params: Promise<{ restaurantId: string; tableId: string; locale: string }>
}) {
  // 解析 params Promise
  const resolvedParams = await params;
  
  // 渲染客户端组件，传递解析后的参数
  return <AuthPageClient
    restaurantId={resolvedParams.restaurantId}
    tableId={resolvedParams.tableId}
    locale={resolvedParams.locale}
  />;
}
