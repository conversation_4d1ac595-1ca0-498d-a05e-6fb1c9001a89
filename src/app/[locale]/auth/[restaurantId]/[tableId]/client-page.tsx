'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Loading from '@/components/common/Loading';

// 客户端组件，接收已解析的参数
export default function AuthPageClient({
  restaurantId,
  tableId,
  locale
}: {
  restaurantId: string;
  tableId: string;
  locale: string;
}) {
  const t = useTranslations();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [guestCount, setGuestCount] = useState(1);
  const [tableNumber, setTableNumber] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 获取表格编号
  useEffect(() => {
    const fetchTableNumber = async () => {
      try {
        const response = await fetch(`/api/tables?id=${tableId}`);
        if (!response.ok) {
          throw new Error(`Error fetching table: ${response.statusText}`);
        }
        const data = await response.json();
        if (data && data.table_number) {
          setTableNumber(data.table_number);
        }
      } catch (error: any) {
        console.error('Error fetching table number:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTableNumber();
  }, [tableId]);

  const handleSubmit = async () => {
    if (guestCount < 1) {
      setError('Please select at least 1 guest');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Call the API to generate a token
      const response = await fetch(`/api/auth/generate-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          restaurantId,
          tableId,
          guestCount,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate token');
      }

      const data = await response.json();

      // Store the token and restaurant ID in localStorage
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('restaurant_id', restaurantId);
      localStorage.setItem('table_id', tableId);
      localStorage.setItem('token_expiry', data.expiry);
      localStorage.setItem('order_id', data.orderId);
      localStorage.setItem('guest_count', guestCount.toString());

      // Redirect to the ordering page and replace the history entry
      // This prevents the user from going back to this page
      router.replace(`/${locale}/customer/${tableId}`);
    } catch (error: any) {
      console.error('Error generating token:', error);
      setError('Failed to authenticate. Please try again.');
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">{t('common.loading')}</h1>
          <p className="text-gray-600 mb-8">{t('customer.preparingTable')}</p>
          <Loading />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">{t('customer.authError')}</h1>
          <p className="text-gray-700">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            {t('customer.tryAgain')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">{t('customer.welcomeToRestaurant')}</h1>
        <p className="text-gray-600 mb-6">
          {t('customer.tableNumber')}: <span className="font-semibold">{tableNumber}</span>
        </p>

        <div className="mb-6">
          <label htmlFor="guestCount" className="block text-sm font-medium text-gray-700 mb-2">
            {t('customer.selectGuestCount')}
          </label>
          <div className="flex items-center justify-center">
            <button
              type="button"
              onClick={() => setGuestCount(Math.max(1, guestCount - 1))}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l"
            >
              -
            </button>
            <div className="bg-gray-100 py-2 px-6 text-center min-w-[60px]">
              {guestCount}
            </div>
            <button
              type="button"
              onClick={() => setGuestCount(guestCount + 1)}
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r"
            >
              +
            </button>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-md font-medium disabled:opacity-50"
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {t('customer.continueToMenu')}
            </span>
          ) : (
            t('customer.continueToMenu')
          )}
        </button>
      </div>
    </div>
  );
}
