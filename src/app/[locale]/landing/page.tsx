'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import LanguageSwitcher from '@/components/common/LanguageSwitcher';
import Head from 'next/head';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

export default function LandingPage() {
  const t = useTranslations('landing');
  const locale = useLocale();
  const router = useRouter();

  const features = [
    {
      title: t('featureMultilingual'),
      description: t('featureMultilingualDesc'),
      icon: '🌐',
    },
    {
      title: t('featureQRCode'),
      description: t('featureQRCodeDesc'),
      icon: '📱',
    },
    {
      title: t('featureRealTime'),
      description: t('featureRealTimeDesc'),
      icon: '⚡',
    },
    {
      title: t('featureKitchen'),
      description: t('featureKitchenDesc'),
      icon: '👨‍🍳',
    },
    {
      title: t('featureAnalytics'),
      description: t('featureAnalyticsDesc'),
      icon: '📊',
    },
    {
      title: t('featureCustomization'),
      description: t('featureCustomizationDesc'),
      icon: '✏️',
    },
  ];

  const steps = [
    {
      title: t('step1'),
      description: t('step1Desc'),
      icon: '📝',
    },
    {
      title: t('step2'),
      description: t('step2Desc'),
      icon: '🍽️',
    },
    {
      title: t('step3'),
      description: t('step3Desc'),
      icon: '📲',
    },
    {
      title: t('step4'),
      description: t('step4Desc'),
      icon: '🔔',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <Head>
        <title>Kyoto Kitchen QR - Modern QR Code Ordering System for Japanese Restaurants</title>
        <meta name="description" content="Streamline your restaurant operations with our QR code ordering system. Support for multiple languages, real-time updates, and kitchen management." />
        <meta name="keywords" content="QR code ordering, restaurant ordering system, digital menu, contactless ordering, Japanese restaurant" />
      </Head>
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-blue-600">{t('title')}</h1>
          </div>
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}/pricing`} className="text-gray-600 hover:text-gray-900">
              {t('viewPricing')}
            </Link>
            <Link href={`/${locale}/register`} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
              {t('registerNow')}
            </Link>
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <motion.section
        className="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <div className="container mx-auto px-4 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-10 md:mb-0">
            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-4"
              variants={fadeIn}
            >
              {t('title')}
            </motion.h2>
            <motion.p
              className="text-xl md:text-2xl mb-6"
              variants={fadeIn}
            >
              {t('subtitle')}
            </motion.p>
            <motion.p
              className="text-lg mb-8"
              variants={fadeIn}
            >
              {t('heroText')}
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
              variants={fadeIn}
            >
              <Link href={`/${locale}/register`} className="bg-white text-blue-600 hover:bg-gray-100 px-6 py-3 rounded-md font-medium text-center">
                {t('getStarted')}
              </Link>
              <Link href={`/${locale}/pricing`} className="bg-transparent border border-white text-white hover:bg-white hover:text-blue-600 px-6 py-3 rounded-md font-medium text-center">
                {t('viewPricing')}
              </Link>
            </motion.div>
            <motion.p
              className="mt-6 text-yellow-300 font-medium"
              variants={fadeIn}
            >
              {t('freeTrial')}
            </motion.p>
          </div>
          <div className="md:w-1/2 flex justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="relative w-full max-w-md"
            >
              <Image
                src="/images/qr-code-ordering.png"
                alt="QR Code Ordering System"
                width={500}
                height={400}
                className="rounded-lg shadow-2xl"
                onError={(e) => {
                  // Fallback if image doesn't exist
                  e.currentTarget.src = "https://placehold.co/500x400/blue/white?text=QR+Code+Ordering";
                }}
              />
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{t('features')}</h2>
            <div className="w-20 h-1 bg-blue-600 mx-auto"></div>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-gray-50 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow"
                variants={fadeIn}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{t('howItWorks')}</h2>
            <div className="w-20 h-1 bg-blue-600 mx-auto"></div>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {steps.map((step, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow"
                variants={fadeIn}
              >
                <div className="text-4xl mb-4">{step.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeIn}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">{t('registerNow')}</h2>
            <p className="text-xl mb-8">{t('freeTrial')}</p>
            <Link href={`/${locale}/register`} className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 rounded-md font-medium text-lg inline-block">
              {t('getStarted')}
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">{t('title')}</h3>
              <p className="mb-4">{t('subtitle')}</p>
              <p>{t('contactEmail')}</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">{t('features')}</h3>
              <ul className="space-y-2">
                {features.slice(0, 4).map((feature, index) => (
                  <li key={index}>{feature.title}</li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">{t('howItWorks')}</h3>
              <ul className="space-y-2">
                {steps.map((step, index) => (
                  <li key={index}>{step.title}</li>
                ))}
              </ul>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-700 text-center">
            <p>&copy; 2024 Kyoto Kitchen QR. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
