import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface RestaurantState {
  restaurantId: string | null;
  isLoading: boolean;
  error: string | null;
  setRestaurantId: (id: string) => void;
  clearRestaurantId: () => void;
  getRestaurantId: () => string | null;
}

// 创建持久化的 Zustand store
export const useRestaurantStore = create<RestaurantState>()(
  persist(
    (set, get) => ({
      restaurantId: null,
      isLoading: false,
      error: null,

      // 设置餐厅 ID
      setRestaurantId: (id: string) => {
        console.log('RestaurantStore - Setting restaurant ID:', id);
        set({ restaurantId: id });

        // 同时更新 localStorage，确保兼容性
        if (typeof window !== 'undefined') {
          localStorage.setItem('restaurant_id', id);
        }
      },

      // 清除餐厅 ID
      clearRestaurantId: () => {
        console.log('RestaurantStore - Clearing restaurant ID');
        set({ restaurantId: null });

        // 同时清除 localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('restaurant_id');
        }
      },

      // 获取餐厅 ID，优先从 store 中获取，如果没有则从 localStorage 中获取
      getRestaurantId: () => {
        const { restaurantId } = get();

        // 如果 store 中有餐厅 ID，直接返回
        if (restaurantId) {
          return restaurantId;
        }

        // 否则尝试从 localStorage 中获取
        if (typeof window !== 'undefined') {
          const localStorageId = localStorage.getItem('restaurant_id');

          if (localStorageId) {
            console.log('RestaurantStore - Found restaurant ID in localStorage:', localStorageId);
            // 更新 store 中的值
            set({ restaurantId: localStorageId });
            return localStorageId;
          }
        }

        console.log('RestaurantStore - No restaurant ID found');
        return null;
      }
    }),
    {
      name: 'restaurant-storage', // localStorage 中的键名
      partialize: (state) => ({ restaurantId: state.restaurantId }), // 只持久化 restaurantId
    }
  )
);
