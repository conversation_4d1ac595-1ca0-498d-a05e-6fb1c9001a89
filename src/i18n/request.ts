import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async ({ locale }) => {
  // Make sure locale is defined
  const resolvedLocale = locale || 'en';

  try {
    return {
      locale: resolvedLocale,
      timeZone: 'Asia/Tokyo', // 添加默认时区设置
      now: new Date(), // 添加当前时间
      formats: {
        dateTime: {
          short: {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          }
        }
      },
      messages: (await import(`./messages/${resolvedLocale}.json`)).default
    };
  } catch (error: any) {
    console.error(`Failed to load messages for locale: ${resolvedLocale}`, error);
    return {
      locale: resolvedLocale,
      timeZone: 'Asia/Tokyo', // 添加默认时区设置
      now: new Date(), // 添加当前时间
      formats: {
        dateTime: {
          short: {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          }
        }
      },
      messages: {}
    };
  }
});
