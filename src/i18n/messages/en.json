{"common": {"language": "Language", "english": "English", "japanese": "日本語", "korean": "한국어", "chinese": "中文", "cart": "<PERSON><PERSON>", "order": "Order", "checkout": "Checkout", "total": "Total", "submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "sort": "Sort", "price": "Price", "quantity": "Quantity", "description": "Description", "name": "Name", "category": "Category", "categories": "Categories", "dish": "Dish", "dishes": "Dishes", "restaurant": "Restaurant", "table": "Table", "status": "Status", "actions": "Actions", "loading": "Loading...", "note": "Note", "all": "All"}, "customer": {"menu": "<PERSON><PERSON>", "addToCart": "Add to Cart", "added": "Added!", "removeFromCart": "Remove from Cart", "emptyCart": "Your cart is empty", "orderPlaced": "Your order has been placed", "continueShopping": "Continue Shopping", "orderAgain": "Order Again", "submittedItems": "Submitted Items", "currentCart": "Current Cart", "quantity": "Quantity", "orderNumberShort": "Order #", "noOrders": "No orders yet", "browseMenu": "Browse the menu to start ordering", "orderHistory": "Order History", "orderDetails": "Order Details", "orderNumber": "Order Number", "orderDate": "Order Date", "orderStatus": "Order Status", "orderTotal": "Order Total", "orderItems": "Order Items", "orderItem": "Order Item", "orderItemPrice": "Price", "orderItemQuantity": "Quantity", "orderItemTotal": "Total", "orderItemName": "Name", "orderItemDescription": "Description", "orderItemCategory": "Category", "orderItemImage": "Image", "orderItemStatus": "Status", "orderItemActions": "Actions", "orderItemEdit": "Edit", "orderItemDelete": "Delete", "orderItemAdd": "Add", "orderItemRemove": "Remove", "welcomeToRestaurant": "Welcome", "tableNumber": "Table Number", "selectGuestCount": "number of people", "continueToMenu": "Continue to Menu"}, "kitchen": {"title": "Kitchen Management", "refresh": "Refresh Orders", "orders": "Orders", "newOrders": "New Orders", "inProgress": "In Progress", "ready": "Ready", "completed": "Completed", "cancelled": "Cancelled", "orderDetails": "Order Details", "startCooking": "Start Cooking", "markAsReady": "<PERSON> as Ready", "markAsCompleted": "<PERSON> as Completed", "markAsAwaitingPayment": "<PERSON> as Awaiting Payment", "markAsCancelled": "<PERSON> as Cancelled", "confirmCancelOrder": "Are you sure you want to cancel the entire order?", "startAllPending": "Start All Pending", "markAllInProgressAsReady": "<PERSON> All In Progress as Ready", "table": "Table", "items": "Items", "notes": "Notes", "status": "Status", "actions": "Actions", "noOrders": "No orders found", "awaitingPayment": "Awaiting Payment", "paid": "Paid", "errorUpdatingOrder": "Error updating order status. Please try again.", "errorUpdatingOrderItem": "Error updating item status. Please try again.", "newOrdersAvailable": "New orders available. Click to refresh.", "inProgressOrders": "In Progress Orders", "awaitingPaymentOrders": "Awaiting Payment Orders", "orderHistory": "Order History", "completedOrders": "Completed Orders", "noOrdersFound": "No orders found for this date", "today": "Today", "yesterday": "Yesterday", "twoDaysAgo": "2 Days Ago", "threeDaysAgo": "3 Days Ago", "statistics": "Statistics", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "topSellingDishes": "Top Selling Dishes", "noData": "No data available", "noStatsAvailable": "No statistics available", "errorFetchingStats": "Error fetching statistics", "todayOrders": "Today's Orders", "todayRevenue": "Today's Revenue", "topDishes": "Top Dishes", "units": "units", "confirmCancelItem": "Are you sure you want to cancel this item?", "errorUpdatingOrderItemQuantity": "Error updating order item quantity", "errorCancellingOrderItem": "Error cancelling order item", "qrCodes": "QR Codes", "printSelected": "Print Selected", "printOrder": "Print Order", "customizeQRCodes": "Customize QR Codes", "uploadLogo": "Upload Logo", "logoSize": "Logo Size", "selectAll": "Select All", "noTablesFound": "No tables found", "downloadQRCode": "Download QR Code", "restaurantQRCodes": "Restaurant QR Codes", "scanToOrder": "Scan to order", "qrCodeNote": "The QR codes generated here are permanent and can be printed for long-term use. When a customer scans the QR code, they will receive a temporary authorization token valid for 1 hour.", "tableManagement": "Table Management", "addTable": "Add Table", "editTable": "Edit Table", "tableNumber": "Table Number", "tableNumberRequired": "Table number is required", "tableNumberInvalid": "Table number can only contain letters, numbers, hyphens and underscores", "tableNumberPlaceholder": "Enter table number, e.g. A1, B2, VIP1", "confirmDeleteTable": "Are you sure you want to delete this table?", "tables": "Tables", "noTables": "No tables found", "createdAt": "Created At", "update": "Update", "manualOrder": "Manual Order", "createManualOrder": "Create Manual Order", "selectTable": "Select Table", "selectDishes": "Select Dishes", "guestCount": "Guest Count", "addDishToOrder": "Add <PERSON>sh to Order", "createOrder": "Create Order", "orderCreated": "Order Created", "orderCreatedMessage": "Order has been successfully created, Order ID: ", "backToKitchen": "Back to Kitchen", "all": "All", "newOrder": "New Order", "appendOrder": "Append", "cancelOrder": "Cancel Order", "submitOrder": "Submit Order", "hasActiveOrder": "This table has an active order, you can append to it", "noActiveOrder": "This table has no active order", "categoryManagement": "Category Management", "addCategory": "Add Category", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "categoryName": "Category Name", "categoryNameRequired": "Category name is required", "categoryAdded": "Category added successfully", "categoryUpdated": "Category updated successfully", "categoryDeleted": "Category deleted successfully", "confirmDeleteCategory": "Are you sure you want to delete this category?", "inputLanguage": "Input Language", "inputLanguageHint": "Please select the language you are comfortable with for entering category names. The system will automatically translate it to other languages.", "sortOrder": "Sort Order", "categories": "Categories", "noCategories": "No categories found", "adding": "Adding...", "updating": "Updating...", "dragToReorder": "Drag category rows to reorder", "dragToReorderHint": "Tip: Click and drag rows to adjust category order", "categoryOrderUpdated": "Category order updated", "dishManagement": "Dish Management", "dishes": "Dishes", "noDishes": "No dishes found", "addDish": "<PERSON><PERSON>", "editDish": "Edit Dish", "dishName": "Dish Name", "dishDescription": "Dish Description", "dishPrice": "Dish Price", "dishCategory": "Dish Category", "dishImage": "Dish Image", "selectCategory": "Select Category", "dishNameRequired": "Dish name is required", "categoryRequired": "Category is required", "invalidPrice": "Invalid price", "dishAdded": "<PERSON><PERSON> added successfully", "dishUpdated": "Dish updated successfully", "dishDeleted": "Dish deleted successfully", "confirmDeleteDish": "Are you sure you want to delete this dish?", "dishOrderUpdated": "Dish order updated", "category": "Category", "price": "Price", "preview": "Preview", "upload": "Upload", "dragDrop": "Drag and drop image here", "or": "or", "browse": "Browse files", "uploading": "Uploading...", "maxSize": "File size cannot exceed 5MB", "invalidType": "Unsupported file type, please upload JPG, PNG, GIF or WebP images", "uploadFailed": "Upload failed, please try again", "logout": "Logout", "signOut": "Sign out"}, "admin": {"dashboard": "Dashboard", "restaurants": "Restaurants", "categories": "Categories", "dishes": "Dishes", "orders": "Orders", "users": "Users", "settings": "Settings", "logout": "Logout", "addRestaurant": "Add Restaurant", "editRestaurant": "Edit Restaurant", "deleteRestaurant": "Delete Restaurant", "addCategory": "Add Category", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "addDish": "<PERSON><PERSON>", "editDish": "Edit Dish", "deleteDish": "Delete Dish", "dishDetails": "Dish Details", "dishName": "Name", "dishDescription": "Description", "dishPrice": "Price", "dishImage": "Image", "dishCategories": "Categories", "dishStatus": "Status", "dishActions": "Actions", "dishEdit": "Edit", "dishDelete": "Delete", "dishAdd": "Add", "dishRemove": "Remove", "dishSearch": "Search", "dishFilter": "Filter", "dishSort": "Sort", "dishSortByName": "Sort by Name", "dishSortByPrice": "Sort by Price", "dishSortByCategory": "Sort by Category", "dishSortByStatus": "Sort by Status", "dishFilterByCategory": "Filter by Category", "dishFilterByStatus": "Filter by Status", "dishFilterByPrice": "Filter by Price", "dishFilterByName": "Filter by Name", "dishFilterByCategoryAll": "All Categories", "dishFilterByStatusAll": "All Statuses", "dishFilterByPriceAll": "All Prices", "dishFilterByNameAll": "All Names", "active": "Active", "inactive": "Inactive", "featured": "Featured", "popular": "Popular", "special": "Special", "discounted": "Discounted", "analytics": {"title": "Analytics", "selectRestaurant": "Select Restaurant", "startDate": "Start Date", "endDate": "End Date", "analysisType": "Analysis Type", "summary": "Summary", "dishes": "Dishes", "tables": "Tables", "time": "Time", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "averageOrderValue": "Average Order Value", "paymentMethods": "Payment Methods", "recentOrders": "Recent Orders", "orderId": "Order ID", "table": "Table", "date": "Date", "items": "Items", "total": "Total", "popularDishes": "Popular Dishes", "topRevenueDishes": "Top Revenue Dishes", "quantity": "Quantity", "revenue": "Revenue", "tablePerformance": "Table Performance", "orderCount": "Order Count", "hourlyDistribution": "Hourly Distribution", "weekdayDistribution": "Weekday Distribution", "noData": "No data available for the selected criteria"}}, "status": {"pending": "Pending", "inProgress": "In Progress", "ready": "Ready", "served": "Served", "completed": "Completed", "cancelled": "Cancelled", "awaitingPayment": "Awaiting Payment", "paid": "Paid"}, "auth": {"signIn": "Sign in", "signOut": "Sign out", "signingIn": "Signing in...", "email": "Email address", "password": "Password", "emailPasswordRequired": "Email and password are required", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "unauthorized": "Unauthorized. Please log in.", "accessDenied": "Access denied. You don't have permission to view this page."}}